# TRAE自动注册工具 - 依赖项说明

## 系统要求
- Windows 10/11 (64位)
- .NET 6.0 SDK 或 Runtime
- Chrome浏览器 (最新版本)

## .NET 包依赖
以下NuGet包会在编译时自动下载：

### 核心依赖
- Microsoft.NET.Sdk (6.0+)
- Microsoft.WindowsDesktop.App (6.0+)

### Web自动化
- Selenium.WebDriver (4.15.0)
- Selenium.WebDriver.ChromeDriver (119.0.6045.10500)

### JSON处理
- Newtonsoft.Json (13.0.3)

### HTTP客户端
- System.Net.Http (4.3.4)

### 日志记录
- Microsoft.Extensions.Logging (7.0.0)
- Microsoft.Extensions.Logging.Console (7.0.0)

## 外部依赖

### Chrome浏览器
- 用途: Web自动化操作
- 版本: 建议使用最新稳定版
- 下载: https://www.google.com/chrome/

### ChromeDriver
- 用途: Selenium WebDriver的Chrome驱动
- 版本: 需要与Chrome浏览器版本匹配
- 自动管理: 项目已包含ChromeDriver，会自动复制到输出目录

## 安装步骤

### 1. 安装.NET 6.0 SDK
```bash
# 下载地址
https://dotnet.microsoft.com/download/dotnet/6.0

# 验证安装
dotnet --version
```

### 2. 安装Chrome浏览器
- 访问 https://www.google.com/chrome/
- 下载并安装最新版本

### 3. 编译项目
```bash
# 方式一：使用编译脚本
build.bat

# 方式二：手动编译
cd TraeAutoRegister
dotnet restore
dotnet build --configuration Release
dotnet publish --configuration Release --runtime win-x64 --self-contained true
```

## 运行时要求

### 网络连接
- 需要访问以下网站：
  - https://www.trae.ai (TRAE官网)
  - https://www.temporam.com (临时邮箱服务)

### 系统权限
- 读写文件权限 (保存配置和账号信息)
- 网络访问权限
- 启动浏览器进程权限

### 防火墙设置
- 允许程序访问网络
- 允许Chrome浏览器网络访问

## 可选组件

### Visual Studio Code
- 用途: 代码编辑和调试
- 扩展: C# Dev Kit

### Visual Studio 2022
- 用途: 完整的IDE开发环境
- 版本: Community版本免费

## 故障排除

### 依赖项问题
1. **NuGet包下载失败**
   ```bash
   dotnet nuget locals all --clear
   dotnet restore --force
   ```

2. **ChromeDriver版本不匹配**
   - 更新Chrome浏览器到最新版本
   - 下载匹配的ChromeDriver版本

3. **.NET版本问题**
   ```bash
   # 检查已安装的.NET版本
   dotnet --list-sdks
   dotnet --list-runtimes
   ```

### 权限问题
- 以管理员身份运行命令提示符
- 检查文件夹写入权限
- 临时关闭杀毒软件

## 开发环境设置

### 推荐IDE配置
```json
// .vscode/settings.json
{
    "dotnet.defaultSolution": "TraeAutoRegister.sln",
    "omnisharp.enableRoslynAnalyzers": true
}
```

### 调试配置
```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch TraeAutoRegister",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/TraeAutoRegister/bin/Debug/net6.0-windows/TraeAutoRegister.exe",
            "cwd": "${workspaceFolder}/TraeAutoRegister",
            "stopAtEntry": false
        }
    ]
}
```

## 性能优化

### 编译优化
- 使用Release配置
- 启用代码裁剪 (PublishTrimmed)
- 单文件发布 (PublishSingleFile)

### 运行时优化
- 调整延迟时间参数
- 使用无头模式减少资源占用
- 合理设置批量数量

---

如有依赖项相关问题，请参考README.md中的故障排除部分。
