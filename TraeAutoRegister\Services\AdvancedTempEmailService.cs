using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Linq;

namespace TraeAutoRegister.Services
{
    public class AdvancedTempEmailService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private string _currentEmail;
        private string _currentToken;
        private readonly Random _random;

        public AdvancedTempEmailService()
        {
            _httpClient = new HttpClient();
            _random = new Random();
            SetupHttpClient();
        }

        private void SetupHttpClient()
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json, text/plain, */*");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
            _httpClient.DefaultRequestHeaders.Add("Pragma", "no-cache");
        }

        public async Task<string> GenerateEmailAsync()
        {
            try
            {
                // 尝试多个临时邮箱服务
                var email = await TryTempMail() ?? 
                           await TryGuerillaMail() ?? 
                           await Try10MinuteMail() ?? 
                           GenerateFallbackEmail();

                _currentEmail = email;
                return email;
            }
            catch (Exception ex)
            {
                throw new Exception($"生成临时邮箱失败: {ex.Message}");
            }
        }

        private async Task<string> TryTempMail()
        {
            try
            {
                // temp-mail.org API
                var response = await _httpClient.GetAsync("https://api.temp-mail.org/request/mail/id/1000/");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var emailMatch = Regex.Match(content, @"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}");
                    if (emailMatch.Success)
                    {
                        return emailMatch.Value;
                    }
                }
            }
            catch
            {
                // 忽略错误，尝试下一个服务
            }
            return null;
        }

        private async Task<string> TryGuerillaMail()
        {
            try
            {
                // guerrillamail API
                var response = await _httpClient.GetAsync("https://api.guerrillamail.com/ajax.php?f=get_email_address");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var json = JsonDocument.Parse(content);
                    if (json.RootElement.TryGetProperty("email_addr", out var emailProp))
                    {
                        var email = emailProp.GetString();
                        if (json.RootElement.TryGetProperty("sid_token", out var tokenProp))
                        {
                            _currentToken = tokenProp.GetString();
                        }
                        return email;
                    }
                }
            }
            catch
            {
                // 忽略错误，尝试下一个服务
            }
            return null;
        }

        private async Task<string> Try10MinuteMail()
        {
            try
            {
                // 10minutemail
                var response = await _httpClient.GetAsync("https://10minutemail.com/session/address");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var json = JsonDocument.Parse(content);
                    if (json.RootElement.TryGetProperty("address", out var addressProp))
                    {
                        return addressProp.GetString();
                    }
                }
            }
            catch
            {
                // 忽略错误
            }
            return null;
        }

        private string GenerateFallbackEmail()
        {
            // 备用方案：生成随机邮箱地址
            var domains = new[] 
            { 
                "temp-mail.org", 
                "guerrillamail.com", 
                "10minutemail.com",
                "tempmail.net",
                "mailinator.com",
                "yopmail.com"
            };

            var username = GenerateRandomUsername();
            var domain = domains[_random.Next(domains.Length)];
            return $"{username}@{domain}";
        }

        private string GenerateRandomUsername()
        {
            var chars = "abcdefghijklmnopqrstuvwxyz0123456789";
            var length = _random.Next(8, 12);
            var result = new char[length];
            
            for (int i = 0; i < length; i++)
            {
                result[i] = chars[_random.Next(chars.Length)];
            }
            
            return new string(result);
        }

        public async Task<string> WaitForVerificationCodeAsync(int timeoutMs = 300000)
        {
            if (string.IsNullOrEmpty(_currentEmail))
                throw new InvalidOperationException("没有可用的邮箱地址");

            var startTime = DateTime.Now;
            var timeout = TimeSpan.FromMilliseconds(timeoutMs);

            while (DateTime.Now - startTime < timeout)
            {
                try
                {
                    var verificationLink = await CheckEmailsAsync();
                    if (!string.IsNullOrEmpty(verificationLink))
                    {
                        return verificationLink;
                    }

                    await Task.Delay(5000); // 每5秒检查一次
                }
                catch
                {
                    // 忽略检查错误，继续等待
                }
            }

            throw new TimeoutException("等待验证邮件超时");
        }

        private async Task<string> CheckEmailsAsync()
        {
            // 尝试不同的邮箱检查方法
            var link = await CheckGuerrillaMailAsync() ?? 
                      await Check10MinuteMailAsync() ?? 
                      await CheckTempMailAsync();

            return link;
        }

        private async Task<string> CheckGuerrillaMailAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_currentToken))
                    return null;

                var url = $"https://api.guerrillamail.com/ajax.php?f=get_email_list&offset=0&sid_token={_currentToken}";
                var response = await _httpClient.GetAsync(url);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var json = JsonDocument.Parse(content);
                    
                    if (json.RootElement.TryGetProperty("list", out var listProp) && listProp.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var email in listProp.EnumerateArray())
                        {
                            if (email.TryGetProperty("mail_subject", out var subjectProp))
                            {
                                var subject = subjectProp.GetString();
                                if (subject.Contains("verify", StringComparison.OrdinalIgnoreCase) ||
                                    subject.Contains("confirm", StringComparison.OrdinalIgnoreCase) ||
                                    subject.Contains("activation", StringComparison.OrdinalIgnoreCase))
                                {
                                    if (email.TryGetProperty("mail_id", out var idProp))
                                    {
                                        var emailId = idProp.GetString();
                                        return await GetVerificationLinkFromEmailAsync(emailId);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch
            {
                // 忽略错误
            }
            return null;
        }

        private async Task<string> Check10MinuteMailAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("https://10minutemail.com/session/messages");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var json = JsonDocument.Parse(content);
                    
                    if (json.RootElement.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var message in json.RootElement.EnumerateArray())
                        {
                            if (message.TryGetProperty("subject", out var subjectProp))
                            {
                                var subject = subjectProp.GetString();
                                if (subject.Contains("verify", StringComparison.OrdinalIgnoreCase) ||
                                    subject.Contains("confirm", StringComparison.OrdinalIgnoreCase))
                                {
                                    if (message.TryGetProperty("bodyPlainText", out var bodyProp))
                                    {
                                        var body = bodyProp.GetString();
                                        var linkMatch = Regex.Match(body, @"https?://[^\s]+verify[^\s]*");
                                        if (linkMatch.Success)
                                        {
                                            return linkMatch.Value;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch
            {
                // 忽略错误
            }
            return null;
        }

        private async Task<string> CheckTempMailAsync()
        {
            try
            {
                // temp-mail.org 邮件检查
                var emailHash = ComputeMD5Hash(_currentEmail.Split('@')[0]);
                var response = await _httpClient.GetAsync($"https://api.temp-mail.org/request/mail/id/{emailHash}/");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var linkMatch = Regex.Match(content, @"https?://[^\s]+verify[^\s]*");
                    if (linkMatch.Success)
                    {
                        return linkMatch.Value;
                    }
                }
            }
            catch
            {
                // 忽略错误
            }
            return null;
        }

        private async Task<string> GetVerificationLinkFromEmailAsync(string emailId)
        {
            try
            {
                var url = $"https://api.guerrillamail.com/ajax.php?f=fetch_email&email_id={emailId}&sid_token={_currentToken}";
                var response = await _httpClient.GetAsync(url);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var json = JsonDocument.Parse(content);
                    
                    if (json.RootElement.TryGetProperty("mail_body", out var bodyProp))
                    {
                        var body = bodyProp.GetString();
                        var linkMatch = Regex.Match(body, @"https?://[^\s""'<>]+verify[^\s""'<>]*");
                        if (linkMatch.Success)
                        {
                            return linkMatch.Value;
                        }
                    }
                }
            }
            catch
            {
                // 忽略错误
            }
            return null;
        }

        private string ComputeMD5Hash(string input)
        {
            using (var md5 = System.Security.Cryptography.MD5.Create())
            {
                var inputBytes = System.Text.Encoding.UTF8.GetBytes(input);
                var hashBytes = md5.ComputeHash(inputBytes);
                return Convert.ToHexString(hashBytes).ToLower();
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
