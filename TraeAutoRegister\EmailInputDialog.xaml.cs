using System.Windows;

namespace TraeAutoRegister
{
    public partial class EmailInputDialog : Window
    {
        public string Email { get; private set; }

        public EmailInputDialog()
        {
            InitializeComponent();
            txtEmail.Focus();
        }

        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            Email = txtEmail.Text.Trim();
            
            if (string.IsNullOrWhiteSpace(Email))
            {
                MessageBox.Show("请输入邮箱地址", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!Email.Contains("@"))
            {
                MessageBox.Show("请输入有效的邮箱地址", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            DialogResult = true;
            Close();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
