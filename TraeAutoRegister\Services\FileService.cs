using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using TraeAutoRegister.Models;

namespace TraeAutoRegister.Services
{
    public class FileService
    {
        private readonly AppConfig _config;

        public FileService(AppConfig config)
        {
            _config = config;
        }

        public async Task SaveAccountAsync(AccountInfo account)
        {
            try
            {
                var fileName = _config.Settings.AccountsFileName;
                var directory = Path.GetDirectoryName(fileName);
                
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 创建文件头（如果文件不存在）
                if (!File.Exists(fileName))
                {
                    var header = "创建时间\t邮箱地址\t密码\t状态\t验证状态\n";
                    await File.WriteAllTextAsync(fileName, header, Encoding.UTF8);
                }

                // 添加账号信息
                var accountLine = $"{account.CreatedAt:yyyy-MM-dd HH:mm:ss}\t{account.Email}\t{account.Password}\t{account.Status}\t{(account.IsVerified ? "已验证" : "未验证")}\n";
                await File.AppendAllTextAsync(fileName, accountLine, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"保存账号信息失败: {ex.Message}");
            }
        }

        public async Task SaveLogAsync(string message)
        {
            try
            {
                var logFileName = $"logs/log_{DateTime.Now:yyyyMMdd}.txt";
                var directory = Path.GetDirectoryName(logFileName);
                
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}\n";
                await File.AppendAllTextAsync(logFileName, logEntry, Encoding.UTF8);
            }
            catch
            {
                // 忽略日志保存错误
            }
        }

        public string GetAccountsFilePath()
        {
            return Path.GetFullPath(_config.Settings.AccountsFileName);
        }

        public bool AccountsFileExists()
        {
            return File.Exists(_config.Settings.AccountsFileName);
        }

        public async Task<string> ReadAccountsFileAsync()
        {
            try
            {
                if (AccountsFileExists())
                {
                    return await File.ReadAllTextAsync(_config.Settings.AccountsFileName, Encoding.UTF8);
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                throw new Exception($"读取账号文件失败: {ex.Message}");
            }
        }

        public void OpenAccountsFileLocation()
        {
            try
            {
                var filePath = GetAccountsFilePath();
                var directory = Path.GetDirectoryName(filePath);
                
                if (Directory.Exists(directory))
                {
                    System.Diagnostics.Process.Start("explorer.exe", directory);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"打开文件位置失败: {ex.Message}");
            }
        }
    }
}
