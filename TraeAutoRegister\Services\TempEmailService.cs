using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;

namespace TraeAutoRegister.Services
{
    public class TempEmailService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private IWebDriver _driver;
        private string _currentEmail;

        public TempEmailService()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36");
        }

        public async Task<string> GenerateEmailAsync()
        {
            try
            {
                // 初始化浏览器
                InitializeBrowser();
                
                // 访问Temporam网站
                _driver.Navigate().GoToUrl("https://www.temporam.com/zh/ems");
                await Task.Delay(3000);

                // 等待页面加载并获取邮箱地址
                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(30));
                
                // 查找邮箱地址元素
                var emailElement = wait.Until(driver => 
                {
                    try
                    {
                        // 尝试多种可能的选择器
                        var selectors = new[]
                        {
                            "input[type='email']",
                            ".email-address",
                            "#email",
                            "[data-email]",
                            ".temp-email"
                        };

                        foreach (var selector in selectors)
                        {
                            try
                            {
                                var element = driver.FindElement(By.CssSelector(selector));
                                if (!string.IsNullOrEmpty(element.GetAttribute("value")) || 
                                    !string.IsNullOrEmpty(element.Text))
                                {
                                    return element;
                                }
                            }
                            catch { }
                        }

                        // 如果找不到，尝试查找包含@符号的文本
                        var allElements = driver.FindElements(By.XPath("//*[contains(text(), '@')]"));
                        foreach (var element in allElements)
                        {
                            var text = element.Text;
                            if (IsValidEmail(text))
                            {
                                return element;
                            }
                        }

                        return null;
                    }
                    catch
                    {
                        return null;
                    }
                });

                if (emailElement != null)
                {
                    _currentEmail = emailElement.GetAttribute("value") ?? emailElement.Text;
                    
                    if (string.IsNullOrEmpty(_currentEmail) || !IsValidEmail(_currentEmail))
                    {
                        // 如果没有找到邮箱，尝试生成一个
                        _currentEmail = await GenerateFallbackEmail();
                    }
                }
                else
                {
                    // 备用方案：生成随机邮箱
                    _currentEmail = await GenerateFallbackEmail();
                }

                return _currentEmail;
            }
            catch (Exception ex)
            {
                throw new Exception($"生成临时邮箱失败: {ex.Message}");
            }
        }

        private Task<string> GenerateFallbackEmail()
        {
            // 生成随机邮箱地址
            var random = new Random();
            var username = $"user{random.Next(100000, 999999)}";
            var domains = new[] { "@temporam.com", "@temp-mail.org", "@guerrillamail.com" };
            var domain = domains[random.Next(domains.Length)];

            return Task.FromResult(username + domain);
        }

        public async Task<string> WaitForVerificationCodeAsync(int timeoutMs = 300000)
        {
            if (string.IsNullOrEmpty(_currentEmail))
                throw new InvalidOperationException("没有可用的邮箱地址");

            var startTime = DateTime.Now;
            var timeout = TimeSpan.FromMilliseconds(timeoutMs);

            while (DateTime.Now - startTime < timeout)
            {
                try
                {
                    var code = await CheckForVerificationCodeAsync();
                    if (!string.IsNullOrEmpty(code))
                    {
                        return code;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"检查邮件时出错: {ex.Message}");
                }

                await Task.Delay(5000); // 每5秒检查一次
            }

            throw new TimeoutException("等待验证码超时");
        }

        private async Task<string> CheckForVerificationCodeAsync()
        {
            try
            {
                if (_driver == null)
                    return null;

                // 刷新页面检查新邮件
                _driver.Navigate().Refresh();
                await Task.Delay(2000);

                // 查找邮件列表
                var emailElements = _driver.FindElements(By.CssSelector(".email-item, .message, .mail-item"));
                
                foreach (var emailElement in emailElements)
                {
                    try
                    {
                        // 点击邮件查看内容
                        emailElement.Click();
                        await Task.Delay(1000);

                        // 获取邮件内容
                        var contentElements = _driver.FindElements(By.CssSelector(".email-content, .message-content, .mail-content"));
                        
                        foreach (var contentElement in contentElements)
                        {
                            var content = contentElement.Text;
                            var code = ExtractVerificationCode(content);
                            if (!string.IsNullOrEmpty(code))
                            {
                                return code;
                            }
                        }
                    }
                    catch { }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        private string ExtractVerificationCode(string content)
        {
            if (string.IsNullOrEmpty(content))
                return null;

            // 多种验证码模式
            var patterns = new[]
            {
                @"验证码[：:\s]*(\d{4,8})",
                @"verification code[：:\s]*(\d{4,8})",
                @"code[：:\s]*(\d{4,8})",
                @"(\d{6})",
                @"(\d{4})",
                @"(\d{5})",
                @"(\d{8})"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(content, pattern, RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return null;
        }

        private void InitializeBrowser()
        {
            if (_driver != null)
                return;

            var options = new ChromeOptions();
            options.AddArgument("--no-sandbox");
            options.AddArgument("--disable-dev-shm-usage");
            options.AddArgument("--disable-gpu");
            options.AddArgument("--window-size=1920,1080");
            options.AddArgument("--disable-extensions");
            options.AddArgument("--disable-plugins");
            options.AddArgument("--log-level=3");
            options.AddArgument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36");

            var service = ChromeDriverService.CreateDefaultService();
            service.HideCommandPromptWindow = true;
            service.SuppressInitialDiagnosticInformation = true;

            _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(60));
            _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(15);
            _driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(60);
        }

        private bool IsValidEmail(string email)
        {
            if (string.IsNullOrEmpty(email))
                return false;

            try
            {
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                return emailRegex.IsMatch(email.Trim());
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            _driver?.Quit();
            _driver?.Dispose();
            _httpClient?.Dispose();
        }
    }
}
