using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using TraeAutoRegister.Models;

namespace TraeAutoRegister.Services
{
    public class ApiRegistrationService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly ConfigService _configService;
        private readonly AppConfig _config;
        private readonly Random _random;

        public ApiRegistrationService(ConfigService configService)
        {
            _configService = configService;
            _config = configService.LoadConfig();
            _random = new Random();

            // 配置HttpClient
            var handler = new HttpClientHandler()
            {
                UseCookies = true
            };

            _httpClient = new HttpClient(handler);
            SetupHttpClient();
        }

        private void SetupHttpClient()
        {
            // 设置通用请求头，模拟真实浏览器
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", GetRandomUserAgent());
            _httpClient.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
            _httpClient.DefaultRequestHeaders.Add("DNT", "1");
            _httpClient.DefaultRequestHeaders.Add("Connection", "keep-alive");
            _httpClient.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Dest", "document");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Mode", "navigate");
            _httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Site", "none");
            _httpClient.DefaultRequestHeaders.Add("Cache-Control", "max-age=0");

            _httpClient.Timeout = TimeSpan.FromSeconds(60);
        }

        private string GetRandomUserAgent()
        {
            var userAgents = _config.UserAgents;
            return userAgents[_random.Next(userAgents.Count)];
        }

        public async Task<RegistrationResult> RegisterAccountAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始API注册流程");

                // 1. 生成临时邮箱
                System.Diagnostics.Debug.WriteLine("正在生成临时邮箱...");
                var email = await GenerateTempEmailAsync();
                if (string.IsNullOrEmpty(email))
                {
                    return new RegistrationResult { Success = false, Message = "生成临时邮箱失败" };
                }

                // 2. 获取注册页面和必要的token
                var registerPageData = await GetRegisterPageDataAsync();
                if (registerPageData == null)
                {
                    return new RegistrationResult { Success = false, Message = "获取注册页面数据失败" };
                }

                // 3. 提交注册请求
                var registrationData = new
                {
                    email = email,
                    password = _config.Settings.DefaultPassword,
                    username = GenerateRandomUsername(),
                    csrf_token = registerPageData.CsrfToken,
                    terms_accepted = true
                };

                var registerResult = await SubmitRegistrationAsync(registrationData);
                if (!registerResult.Success)
                {
                    return registerResult;
                }

                // 4. 等待验证邮件并获取验证链接
                var verificationLink = await WaitForVerificationEmailAsync(email);
                if (string.IsNullOrEmpty(verificationLink))
                {
                    return new RegistrationResult { Success = false, Message = "未收到验证邮件" };
                }

                // 5. 点击验证链接完成注册
                var verifyResult = await VerifyEmailAsync(verificationLink);
                if (!verifyResult)
                {
                    return new RegistrationResult { Success = false, Message = "邮箱验证失败" };
                }

                // 6. 保存账号信息
                var account = new AccountInfo
                {
                    Email = email,
                    Password = _config.Settings.DefaultPassword,
                    CreatedAt = DateTime.Now,
                    Status = "已验证"
                };

                if (_config.Settings.SaveAccountsToFile)
                {
                    await SaveAccountToFileAsync(account);
                }

                return new RegistrationResult
                {
                    Success = true,
                    Account = account,
                    Message = "注册成功"
                };
            }
            catch (Exception ex)
            {
                return new RegistrationResult
                {
                    Success = false,
                    Message = $"注册过程异常: {ex.Message}"
                };
            }
        }

        private async Task<string> GenerateTempEmailAsync()
        {
            try
            {
                // 使用10minutemail API
                var response = await _httpClient.GetAsync("https://10minutemail.com/10MinuteMail/index.html");
                var content = await response.Content.ReadAsStringAsync();

                // 从页面中提取邮箱地址
                var emailMatch = Regex.Match(content, @"id=""mail_address""[^>]*value=""([^""]+)""");
                if (emailMatch.Success)
                {
                    return emailMatch.Groups[1].Value;
                }

                // 备用方案：生成随机邮箱
                var username = $"user{_random.Next(100000, 999999)}";
                var domains = new[] { "@guerrillamail.com", "@temp-mail.org", "@10minutemail.com" };
                return username + domains[_random.Next(domains.Length)];
            }
            catch
            {
                // 如果临时邮箱服务失败，使用随机生成
                var username = $"user{_random.Next(100000, 999999)}";
                return username + "@temp-mail.org";
            }
        }

        private async Task<RegisterPageData> GetRegisterPageDataAsync()
        {
            try
            {
                // 访问TRAE注册页面
                var response = await _httpClient.GetAsync("https://trae.to/register");
                var content = await response.Content.ReadAsStringAsync();

                // 提取CSRF token
                var csrfMatch = Regex.Match(content, @"name=""_token""[^>]*value=""([^""]+)""");
                var csrfToken = csrfMatch.Success ? csrfMatch.Groups[1].Value : "";

                return new RegisterPageData
                {
                    CsrfToken = csrfToken,
                    Content = content
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"获取注册页面失败: {ex.Message}");
            }
        }

        private async Task<RegistrationResult> SubmitRegistrationAsync(object registrationData)
        {
            try
            {
                var json = JsonSerializer.Serialize(registrationData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // 设置注册请求的特定头部
                content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

                // 转换为表单数据
                var formData = new List<KeyValuePair<string, string>>();
                var data = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                foreach (var kvp in data)
                {
                    formData.Add(new KeyValuePair<string, string>(kvp.Key, kvp.Value?.ToString() ?? ""));
                }

                var formContent = new FormUrlEncodedContent(formData);

                var response = await _httpClient.PostAsync("https://trae.to/register", formContent);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    // 检查响应内容判断是否成功
                    if (responseContent.Contains("verification") || responseContent.Contains("verify") || 
                        responseContent.Contains("check your email"))
                    {
                        return new RegistrationResult { Success = true };
                    }
                }

                return new RegistrationResult
                {
                    Success = false,
                    Message = $"注册请求失败: {response.StatusCode}"
                };
            }
            catch (Exception ex)
            {
                return new RegistrationResult
                {
                    Success = false,
                    Message = $"提交注册数据失败: {ex.Message}"
                };
            }
        }

        private async Task<string> WaitForVerificationEmailAsync(string email)
        {
            // 这里需要实现邮箱检查逻辑
            // 由于临时邮箱服务的API各不相同，这里提供一个通用的等待逻辑
            var maxWaitTime = TimeSpan.FromMilliseconds(_config.Settings.MaxEmailWaitTime);
            var startTime = DateTime.Now;

            while (DateTime.Now - startTime < maxWaitTime)
            {
                try
                {
                    // 检查邮箱是否有新邮件
                    var verificationLink = await CheckEmailForVerificationLinkAsync(email);
                    if (!string.IsNullOrEmpty(verificationLink))
                    {
                        return verificationLink;
                    }

                    await Task.Delay(_config.Settings.EmailCheckInterval);
                }
                catch
                {
                    // 忽略检查邮箱时的错误，继续等待
                }
            }

            return null;
        }

        private async Task<string> CheckEmailForVerificationLinkAsync(string email)
        {
            try
            {
                // 这里需要根据使用的临时邮箱服务实现具体的邮件检查逻辑
                // 示例：检查10minutemail的邮件
                var response = await _httpClient.GetAsync("https://10minutemail.com/10MinuteMail/CheckMail.html");
                var content = await response.Content.ReadAsStringAsync();

                // 从邮件内容中提取验证链接
                var linkMatch = Regex.Match(content, @"https://trae\.to/verify/[^""'\s]+");
                if (linkMatch.Success)
                {
                    return linkMatch.Value;
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        private async Task<bool> VerifyEmailAsync(string verificationLink)
        {
            try
            {
                var response = await _httpClient.GetAsync(verificationLink);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private string GenerateRandomUsername()
        {
            var prefixes = new[] { "user", "player", "gamer", "pro", "master", "elite" };
            var prefix = prefixes[_random.Next(prefixes.Length)];
            var number = _random.Next(1000, 9999);
            return $"{prefix}{number}";
        }

        private async Task SaveAccountToFileAsync(AccountInfo account)
        {
            try
            {
                var accountInfo = $"{account.Email}:{account.Password} - {account.CreatedAt:yyyy-MM-dd HH:mm:ss}\n";
                await System.IO.File.AppendAllTextAsync(_config.Settings.AccountsFileName, accountInfo);
            }
            catch
            {
                // 忽略保存文件的错误
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }

        private class RegisterPageData
        {
            public string CsrfToken { get; set; }
            public string Content { get; set; }
        }
    }
}
