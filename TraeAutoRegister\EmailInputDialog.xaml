<Window x:Class="TraeAutoRegister.EmailInputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="输入邮箱地址" Height="250" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">
    
    <Window.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#6366F1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5B5FE8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#4F46E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 取消按钮样式 -->
        <Style x:Key="CancelButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#6B7280"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5B6470"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#4B5563"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 现代化文本框样式 -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="#F8FAFC"/>
            <Setter Property="BorderBrush" Value="#E2E8F0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#6366F1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="12" Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="4" BlurRadius="20"/>
        </Border.Effect>
        
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题 -->
            <TextBlock Grid.Row="0" Text="📧 验证邮箱"
                       FontSize="20" FontWeight="Bold"
                       Foreground="#1E293B"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,20"/>

            <!-- 说明文字 -->
            <TextBlock Grid.Row="1" 
                       Text="请输入需要验证的邮箱地址（支持Temporam邮箱）"
                       FontSize="14" Foreground="#64748B"
                       HorizontalAlignment="Center"
                       TextWrapping="Wrap"
                       Margin="0,0,0,15"/>

            <!-- 邮箱输入框 -->
            <TextBox Grid.Row="2" x:Name="txtEmail"
                     Style="{StaticResource ModernTextBox}"
                     Width="300"
                     Margin="0,0,0,20"
                     Text="<EMAIL>"/>

            <!-- 按钮区域 -->
            <StackPanel Grid.Row="3" Orientation="Horizontal"
                        HorizontalAlignment="Center">
                <Button x:Name="btnOK"
                        Content="验证"
                        Style="{StaticResource ModernButton}"
                        Width="100" Margin="0,0,10,0"
                        Click="BtnOK_Click"/>
                <Button x:Name="btnCancel"
                        Content="取消"
                        Style="{StaticResource CancelButton}"
                        Width="100"
                        Click="BtnCancel_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
