using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using TraeAutoRegister.Models;

namespace TraeAutoRegister.Services
{
    public class HybridRegistrationService : IDisposable
    {
        private readonly ConfigService _configService;
        private readonly AppConfig _config;
        private readonly PureRealApiRegistrationService _apiService;
        private readonly RegistrationService _browserService;
        private readonly bool _useApiMode;

        public event Action<string> StatusUpdated;
        public event Action<string> LogUpdated;
        public event Action<int> ProgressUpdated;

        public HybridRegistrationService(ConfigService configService, bool useApiMode = true)
        {
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));
            _config = configService.LoadConfig() ?? new AppConfig();
            _useApiMode = useApiMode;

            // 确保Settings不为空
            if (_config.Settings == null)
            {
                _config.Settings = new Models.Settings();
            }

            System.Diagnostics.Debug.WriteLine($"HybridRegistrationService初始化: API模式={useApiMode}");

            if (_useApiMode)
            {
                _apiService = new PureRealApiRegistrationService(configService);
                _apiService.LogUpdated += (message) => LogUpdated?.Invoke(message);
                System.Diagnostics.Debug.WriteLine("PureRealApiRegistrationService已创建");
            }
            else
            {
                _browserService = new RegistrationService(configService);
                System.Diagnostics.Debug.WriteLine("RegistrationService已创建");
            }
        }

        public async Task<RegistrationResult> RegisterAccountAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                OnLogUpdated("🔄 HybridRegistrationService.RegisterAccountAsync 开始");
                OnLogUpdated($"📊 使用API模式: {_useApiMode}");
                OnLogUpdated($"🔍 _apiService状态: {(_apiService != null ? "已初始化" : "为null")}");

                if (_useApiMode)
                {
                    OnStatusUpdated("使用API模式注册...");
                    OnLogUpdated("开始API注册流程 - 更快、更稳定");

                    OnLogUpdated("调用PureRealApiRegistrationService.RegisterAccountAsync");

                    if (_apiService == null)
                    {
                        OnLogUpdated("❌ _apiService为null!");
                        throw new InvalidOperationException("API服务未初始化");
                    }

                    OnLogUpdated("📡 开始调用_apiService.RegisterAccountAsync...");
                    var result = await _apiService.RegisterAccountAsync(cancellationToken);
                    OnLogUpdated("📡 _apiService.RegisterAccountAsync调用完成");
                    OnLogUpdated($"API注册结果: Success={result?.Success}, Message={result?.Message}");

                    if (result != null && result.Success)
                    {
                        OnLogUpdated($"✓ API注册成功: {result.Account?.Email}");
                        OnStatusUpdated("API注册完成!");
                        
                        // 保存账号信息
                        if (_config?.Settings?.SaveAccountsToFile == true)
                        {
                            await SaveAccountToFileAsync(result.Account);
                        }
                    }
                    else
                    {
                        var errorMessage = result?.Message ?? "API返回null结果";
                        OnLogUpdated($"✗ API注册失败: {errorMessage}");
                        OnStatusUpdated("API注册失败，尝试浏览器模式...");

                        // API失败时暂时不回退到浏览器模式，直接返回失败结果
                        OnLogUpdated("⚠️ 暂时禁用浏览器模式回退，直接返回API失败结果");
                    }

                    return result ?? new RegistrationResult { Success = false, Message = "API返回null结果" };
                }
                else
                {
                    OnStatusUpdated("使用浏览器模式注册...");
                    OnLogUpdated("开始浏览器注册流程");
                    return await _browserService.RegisterAccountAsync(cancellationToken);
                }
            }
            catch (Exception ex)
            {
                OnLogUpdated($"注册失败: {ex.Message}");
                OnStatusUpdated("注册失败");
                return new RegistrationResult
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        private async Task SaveAccountToFileAsync(AccountInfo account)
        {
            try
            {
                if (account == null)
                {
                    OnLogUpdated("账号信息为空，跳过保存");
                    return;
                }

                var fileName = _config?.Settings?.AccountsFileName ?? "registered_accounts.txt";
                var accountInfo = $"{account.Email}:{account.Password} - {account.CreatedAt:yyyy-MM-dd HH:mm:ss} - {account.Status}\n";

                await File.AppendAllTextAsync(fileName, accountInfo);
                OnLogUpdated($"账号信息已保存到: {fileName}");
            }
            catch (Exception ex)
            {
                OnLogUpdated($"保存账号信息失败: {ex.Message}");
            }
        }

        private void OnStatusUpdated(string status)
        {
            StatusUpdated?.Invoke(status);
        }

        private void OnLogUpdated(string message)
        {
            LogUpdated?.Invoke(message);
        }

        private void OnProgressUpdated(int progress)
        {
            ProgressUpdated?.Invoke(progress);
        }

        public void SwitchToApiMode()
        {
            // 可以在运行时切换模式
        }

        public void SwitchToBrowserMode()
        {
            // 可以在运行时切换模式
        }

        public void Dispose()
        {
            _apiService?.Dispose();
            _browserService?.Dispose();
        }
    }
}
