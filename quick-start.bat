@echo off
title TRAE自动注册工具 - 快速启动
echo ========================================
echo TRAE自动注册工具 - 快速启动
echo ========================================
echo.
echo 选择操作:
echo 1. 测试环境
echo 2. 编译程序
echo 3. 运行程序 (调试模式)
echo 4. 运行程序 (发布版本)
echo 5. 查看帮助
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto test
if "%choice%"=="2" goto build
if "%choice%"=="3" goto run_debug
if "%choice%"=="4" goto run_release
if "%choice%"=="5" goto help
if "%choice%"=="6" goto exit
goto invalid

:test
echo 正在运行环境测试...
call test.bat
goto menu

:build
echo 正在编译程序...
call build.bat
goto menu

:run_debug
echo 正在启动调试版本...
cd TraeAutoRegister
dotnet run --configuration Debug
cd ..
goto menu

:run_release
echo 正在启动发布版本...
if exist "dist\TraeAutoRegister.exe" (
    cd dist
    TraeAutoRegister.exe
    cd ..
) else (
    echo 发布版本不存在，请先编译程序
    pause
)
goto menu

:help
echo.
echo ========================================
echo 使用说明:
echo ========================================
echo 1. 首次使用请先运行"测试环境"
echo 2. 测试通过后运行"编译程序"
echo 3. 编译成功后可以运行程序
echo 4. 调试模式：显示详细信息，便于调试
echo 5. 发布版本：优化后的独立可执行文件
echo.
echo 详细说明请查看 README.md 文件
echo ========================================
pause
goto menu

:invalid
echo 无效选择，请重新输入
pause
goto menu

:menu
cls
echo ========================================
echo TRAE自动注册工具 - 快速启动
echo ========================================
echo.
echo 选择操作:
echo 1. 测试环境
echo 2. 编译程序
echo 3. 运行程序 (调试模式)
echo 4. 运行程序 (发布版本)
echo 5. 查看帮助
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto test
if "%choice%"=="2" goto build
if "%choice%"=="3" goto run_debug
if "%choice%"=="4" goto run_release
if "%choice%"=="5" goto help
if "%choice%"=="6" goto exit
goto invalid

:exit
echo 感谢使用！
exit /b 0
