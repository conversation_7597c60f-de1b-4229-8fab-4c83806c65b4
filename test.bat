@echo off
echo ========================================
echo TRAE自动注册工具 - 测试脚本
echo ========================================

:: 检查.NET SDK
echo 正在检查.NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到.NET SDK
    goto :end
) else (
    echo ✅ .NET SDK 已安装
)

:: 检查Chrome浏览器
echo 正在检查Chrome浏览器...
reg query "HKEY_CURRENT_USER\Software\Google\Chrome\BLBeacon" /v version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Chrome浏览器
) else (
    echo ✅ Chrome浏览器 已安装
)

:: 检查项目文件
echo 正在检查项目文件...
if exist "TraeAutoRegister\TraeAutoRegister.csproj" (
    echo ✅ 项目文件存在
) else (
    echo ❌ 项目文件不存在
    goto :end
)

:: 检查配置文件
if exist "TraeAutoRegister\config.json" (
    echo ✅ 配置文件存在
) else (
    echo ❌ 配置文件不存在
    goto :end
)

:: 测试编译
echo 正在测试编译...
cd TraeAutoRegister
dotnet build --configuration Debug --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    cd ..
    goto :end
) else (
    echo ✅ 编译成功
)
cd ..

:: 检查网络连接
echo 正在检查网络连接...
ping www.trae.ai -n 1 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 无法连接到TRAE网站
) else (
    echo ✅ 网络连接正常
)

echo ========================================
echo 测试完成！
echo 如果所有项目都显示 ✅，则可以正常使用程序
echo 如果有 ❌ 项目，请参考README.md进行修复
echo ========================================

:end
pause
