using System;
using System.Threading.Tasks;
using System.Diagnostics;
using System.IO;
using System.Threading;

namespace TraeAutoRegister.Services
{
    public class BrowserLoginService
    {
        public event Action<string> LogUpdated;

        private void Log(string message)
        {
            Console.WriteLine(message);
            System.Diagnostics.Debug.WriteLine(message);
            LogUpdated?.Invoke(message);
        }

        public async Task<bool> LoginWithBrowserAsync(string email, string password)
        {
            try
            {
                Log($"🌐 开始浏览器自动登录: {email}");

                // 方案1: 使用默认浏览器打开预填充的登录页面
                var success = await OpenBrowserWithPrefilledDataAsync(email, password);
                
                if (!success)
                {
                    // 方案2: 如果方案1失败，尝试使用JavaScript注入
                    Log("⚠️ 预填充方案失败，尝试JavaScript注入方案...");
                    success = await OpenBrowserWithJavaScriptAsync(email, password);
                }

                return success;
            }
            catch (Exception ex)
            {
                Log($"❌ 浏览器登录异常: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> OpenBrowserWithPrefilledDataAsync(string email, string password)
        {
            try
            {
                Log("🔧 方案1: 使用预填充URL打开浏览器");

                // 创建一个临时HTML文件，包含自动填充和提交的JavaScript
                var htmlContent = CreateAutoLoginHtml(email, password);
                var tempFile = Path.Combine(Path.GetTempPath(), $"trae_login_{Guid.NewGuid():N}.html");
                
                await File.WriteAllTextAsync(tempFile, htmlContent);
                Log($"📄 创建临时登录页面: {tempFile}");

                // 打开浏览器
                var processInfo = new ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                };

                Process.Start(processInfo);
                Log("✅ 浏览器已打开，正在自动填充登录信息...");

                // 等待一段时间后删除临时文件
                _ = Task.Run(async () =>
                {
                    await Task.Delay(30000); // 30秒后删除
                    try
                    {
                        if (File.Exists(tempFile))
                        {
                            File.Delete(tempFile);
                            Log("🗑️ 临时文件已清理");
                        }
                    }
                    catch { }
                });

                return true;
            }
            catch (Exception ex)
            {
                Log($"❌ 预填充方案失败: {ex.Message}");
                return false;
            }
        }

        private string CreateAutoLoginHtml(string email, string password)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <title>TRAE自动登录</title>
    <meta charset='utf-8'>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }}
        .container {{
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }}
        .logo {{
            font-size: 48px;
            margin-bottom: 20px;
        }}
        .status {{
            font-size: 18px;
            margin: 20px 0;
            color: #333;
        }}
        .info {{
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #1976d2;
        }}
        .loading {{
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }}
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='logo'>🚀</div>
        <h1>TRAE自动登录</h1>
        <div class='status' id='status'>
            <div class='loading'></div>
            正在准备登录...
        </div>
        <div class='info'>
            <strong>账号信息:</strong><br>
            邮箱: {email}<br>
            密码: {password.Substring(0, 3)}***
        </div>
        <div id='instructions' style='display:none; color: #666; margin-top: 20px;'>
            <p>如果没有自动跳转，请手动访问 <a href='https://www.trae.ai/login' target='_blank'>TRAE登录页面</a></p>
            <p>然后手动输入上述账号信息</p>
        </div>
    </div>

    <script>
        let step = 0;
        const steps = [
            '正在准备登录...',
            '正在打开TRAE登录页面...',
            '正在填充登录信息...',
            '请在新窗口中确认登录'
        ];

        function updateStatus() {{
            if (step < steps.length) {{
                document.getElementById('status').innerHTML = 
                    '<div class=""loading""></div> ' + steps[step];
                step++;
                setTimeout(updateStatus, 1500);
            }} else {{
                document.getElementById('status').innerHTML = 
                    '✅ 准备完成，请在新窗口中确认登录';
                document.getElementById('instructions').style.display = 'block';
            }}
        }}

        // 开始状态更新
        setTimeout(updateStatus, 1000);

        // 2秒后打开TRAE登录页面
        setTimeout(function() {{
            // 创建一个新窗口并尝试自动填充
            const loginWindow = window.open('https://www.trae.ai/login', '_blank');

            // 等待页面加载后尝试自动填充
            setTimeout(function() {{
                try {{
                    // 尝试在新窗口中执行自动填充脚本
                    const script = `
                        // 等待页面完全加载
                        function waitForElement(selector, callback) {{
                            const element = document.querySelector(selector);
                            if (element) {{
                                callback(element);
                            }} else {{
                                setTimeout(() => waitForElement(selector, callback), 500);
                            }}
                        }}

                        // 尝试填充邮箱
                        waitForElement('input[type=""email""], input[name=""email""], input[placeholder*=""email""], input[placeholder*=""邮箱""]', function(emailInput) {{
                            emailInput.value = '{email}';
                            emailInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            emailInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            console.log('邮箱已自动填充');
                        }});

                        // 尝试填充密码
                        waitForElement('input[type=""password""], input[name=""password""], input[placeholder*=""password""], input[placeholder*=""密码""]', function(passwordInput) {{
                            passwordInput.value = '{password}';
                            passwordInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            passwordInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            console.log('密码已自动填充');
                        }});

                        // 高亮登录按钮
                        setTimeout(function() {{
                            const loginButtons = document.querySelectorAll('button[type=""submit""], button:contains(""登录""), button:contains(""Log in""), .login-btn, #login-btn');
                            loginButtons.forEach(btn => {{
                                if (btn) {{
                                    btn.style.border = '3px solid #ff6b6b';
                                    btn.style.boxShadow = '0 0 10px #ff6b6b';
                                    btn.style.animation = 'pulse 1s infinite';
                                }}
                            }});
                        }}, 2000);
                    `;

                    // 尝试在新窗口中执行脚本
                    if (loginWindow && !loginWindow.closed) {{
                        loginWindow.eval(script);
                    }}

                    console.log('自动填充脚本已注入');
                }} catch (e) {{
                    console.log('无法自动填充，需要手动输入:', e);

                    // 如果自动填充失败，至少复制账号信息到剪贴板
                    try {{
                        navigator.clipboard.writeText('{email}').then(() => {{
                            console.log('邮箱已复制到剪贴板');
                        }});
                    }} catch (clipboardError) {{
                        console.log('无法复制到剪贴板');
                    }}
                }}
            }}, 3000);
        }}, 2000);
    </script>
</body>
</html>";
        }

        private async Task<bool> OpenBrowserWithJavaScriptAsync(string email, string password)
        {
            try
            {
                Log("🔧 方案2: 直接打开TRAE登录页面");

                // 直接打开TRAE登录页面
                var processInfo = new ProcessStartInfo
                {
                    FileName = "https://www.trae.ai/login",
                    UseShellExecute = true
                };

                Process.Start(processInfo);

                Log("✅ TRAE登录页面已打开");
                Log($"📧 请手动输入邮箱: {email}");
                Log($"🔐 请手动输入密码: {password}");

                // 尝试复制邮箱到剪贴板
                try
                {
                    System.Windows.Clipboard.SetText(email);
                    Log("📋 邮箱已复制到剪贴板，可以直接粘贴");
                }
                catch
                {
                    Log("⚠️ 无法复制到剪贴板");
                }

                return true;
            }
            catch (Exception ex)
            {
                Log($"❌ JavaScript方案失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 简单的浏览器登录方法 - 打开登录页面并显示账号信息
        /// </summary>
        public async Task<bool> SimpleLoginWithBrowserAsync(string email, string password)
        {
            try
            {
                Log($"🌐 简单浏览器登录: {email}");

                // 直接打开TRAE登录页面
                var processInfo = new ProcessStartInfo
                {
                    FileName = "https://www.trae.ai/login",
                    UseShellExecute = true
                };

                Process.Start(processInfo);

                // 复制邮箱到剪贴板
                try
                {
                    System.Windows.Clipboard.SetText(email);
                    Log("📋 邮箱已复制到剪贴板");
                }
                catch { }

                Log("✅ 登录页面已打开");
                Log($"📧 邮箱: {email} (已复制到剪贴板)");
                Log($"🔐 密码: {password}");
                Log("💡 请在浏览器中手动粘贴邮箱并输入密码");

                return true;
            }
            catch (Exception ex)
            {
                Log($"❌ 简单浏览器登录失败: {ex.Message}");
                return false;
            }
        }
    }
}
