<Window x:Class="TraeAutoRegister.ProgressWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="注册进度" Height="600" Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">
    
    <!-- 主容器 -->
    <Border Background="White" CornerRadius="12" Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.2" ShadowDepth="4" BlurRadius="20"/>
        </Border.Effect>
        
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="2*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🚀 注册进度" FontSize="18" FontWeight="Bold" 
                           Foreground="#1E293B" HorizontalAlignment="Left"/>
                <Button x:Name="btnClose" Content="✕" 
                        HorizontalAlignment="Right" VerticalAlignment="Top"
                        Width="24" Height="24" Background="Transparent" 
                        BorderThickness="0" FontSize="12" Foreground="#94A3B8"
                        Click="BtnClose_Click"/>
            </Grid>

            <!-- 当前状态 -->
            <Border Grid.Row="1" Background="#F8FAFC" CornerRadius="8" 
                    Padding="15" Margin="0,0,0,15">
                <StackPanel>
                    <TextBlock Text="当前状态" FontSize="12" Foreground="#64748B" 
                               FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBlock x:Name="txtCurrentStatus" Text="准备开始..." 
                               FontSize="14" Foreground="#1E293B" FontWeight="SemiBold"/>
                </StackPanel>
            </Border>

            <!-- 进度条 -->
            <StackPanel Grid.Row="2" Margin="0,0,0,15">
                <Grid Margin="0,0,0,8">
                    <TextBlock Text="总体进度" FontSize="12" Foreground="#64748B" 
                               HorizontalAlignment="Left"/>
                    <TextBlock x:Name="txtProgress" Text="0%" FontSize="12" 
                               Foreground="#6366F1" FontWeight="Medium"
                               HorizontalAlignment="Right"/>
                </Grid>
                <ProgressBar x:Name="progressBar" Height="8" 
                             Background="#F1F5F9" Foreground="#6366F1"
                             Minimum="0" Maximum="100" Value="0"/>
            </StackPanel>

            <!-- 统计信息 -->
            <Border Grid.Row="3" Background="#F0FDF4" CornerRadius="8" 
                    Padding="15" Margin="0,0,0,15">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                        <TextBlock x:Name="txtSuccessCount" Text="0" 
                                   FontSize="20" FontWeight="Bold" Foreground="#10B981"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="成功" FontSize="12" Foreground="#059669"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                        <TextBlock x:Name="txtFailedCount" Text="0" 
                                   FontSize="20" FontWeight="Bold" Foreground="#EF4444"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="失败" FontSize="12" Foreground="#DC2626"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                        <TextBlock x:Name="txtTotalCount" Text="0" 
                                   FontSize="20" FontWeight="Bold" Foreground="#6366F1"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="总计" FontSize="12" Foreground="#4F46E5"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 日志区域 -->
            <Border Grid.Row="4" Background="#1E293B" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 日志标题和操作 -->
                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <TextBlock Text="📝 详细日志" FontSize="12"
                                   Foreground="#94A3B8" FontWeight="Medium"
                                   HorizontalAlignment="Left"/>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button x:Name="btnCopyLog" Content="📋 复制日志"
                                    Background="#374151" Foreground="#D1D5DB"
                                    Padding="8,4" BorderThickness="0"
                                    FontSize="10" Click="BtnCopyLog_Click">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="4" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Button.Template>
                            </Button>
                            <Button x:Name="btnClearLog" Content="🗑️ 清空"
                                    Background="#374151" Foreground="#D1D5DB"
                                    Padding="8,4" Margin="5,0,0,0" BorderThickness="0"
                                    FontSize="10" Click="BtnClearLog_Click">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="4" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Button.Template>
                            </Button>
                        </StackPanel>
                    </Grid>

                    <!-- 日志内容 -->
                    <Border Grid.Row="1" Background="#0F172A" CornerRadius="4">
                        <ScrollViewer x:Name="logScrollViewer"
                                      VerticalScrollBarVisibility="Auto"
                                      HorizontalScrollBarVisibility="Auto"
                                      Padding="10">
                            <TextBlock x:Name="txtLog" Text="等待开始..."
                                       FontFamily="Consolas" FontSize="12"
                                       Foreground="#10B981" TextWrapping="Wrap"
                                       LineHeight="18"/>
                        </ScrollViewer>
                    </Border>
                </Grid>
            </Border>

            <!-- 控制按钮 -->
            <StackPanel Grid.Row="5" Orientation="Horizontal"
                        HorizontalAlignment="Right" Margin="0,15,0,0">
                <Button x:Name="btnCancel" Content="取消"
                        Background="#EF4444" Foreground="White"
                        Padding="15,8" Margin="0,0,10,0"
                        BorderThickness="0" FontSize="12" FontWeight="Medium"
                        Click="BtnCancel_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center"
                                                VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
                <Button x:Name="btnMinimize" Content="最小化"
                        Background="#6B7280" Foreground="White"
                        Padding="15,8" BorderThickness="0"
                        FontSize="12" FontWeight="Medium"
                        Click="BtnMinimize_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center"
                                                VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
