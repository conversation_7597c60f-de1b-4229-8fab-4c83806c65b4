using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using System.Threading;
using System.Text.RegularExpressions;

namespace TraeAutoRegister.Services
{
    public class SimpleTempEmailService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private string _currentEmail;
        private string _currentDomain;

        public SimpleTempEmailService()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36");
        }

        public async Task<string> GenerateEmailAsync()
        {
            try
            {
                // 使用简单的随机邮箱生成
                var random = new Random();
                var username = $"user{random.Next(100000, 999999)}";
                var domains = new[] { "@temporam.com", "@temp-mail.org", "@guerrillamail.com", "@10minutemail.com" };
                _currentDomain = domains[random.Next(domains.Length)];
                _currentEmail = username + _currentDomain;

                return _currentEmail;
            }
            catch (Exception ex)
            {
                throw new Exception($"生成临时邮箱失败: {ex.Message}");
            }
        }

        public async Task<string> WaitForVerificationCodeAsync(int timeoutMs = 300000)
        {
            if (string.IsNullOrEmpty(_currentEmail))
                throw new InvalidOperationException("没有可用的邮箱地址");

            // 模拟等待验证码的过程
            // 在实际应用中，这里应该实现真正的邮箱检查逻辑
            // 为了演示目的，我们生成一个模拟的验证码
            
            var startTime = DateTime.Now;
            var timeout = TimeSpan.FromMilliseconds(timeoutMs);
            var random = new Random();

            // 模拟等待30-60秒后返回验证码
            var waitTime = random.Next(30000, 60000);
            await Task.Delay(Math.Min(waitTime, timeoutMs));

            if (DateTime.Now - startTime >= timeout)
            {
                throw new TimeoutException("等待验证码超时");
            }

            // 生成6位数字验证码
            var code = random.Next(100000, 999999).ToString();
            return code;
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
