using System;
using System.Threading;
using System.Threading.Tasks;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using TraeAutoRegister.Models;

namespace TraeAutoRegister.Services
{
    public class WebAutomationService : IDisposable
    {
        private IWebDriver _driver;
        private readonly AppConfig _config;
        private readonly Random _random;

        public WebAutomationService(AppConfig config)
        {
            _config = config;
            _random = new Random();
        }

        public async Task<bool> NavigateToSignUpPageAsync()
        {
            try
            {
                InitializeBrowser();
                
                // 访问登录页面
                _driver.Navigate().GoToUrl(_config.Urls.TraeLogin);
                await Task.Delay(_config.Settings.DelayBetweenSteps);

                // 查找并点击注册链接
                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(30));
                
                var signUpSelectors = new[]
                {
                    "a[href*='sign-up']",
                    "a[href*='signup']",
                    "a[href*='register']",
                    ".sign-up",
                    ".signup",
                    ".register",
                    "//a[contains(text(), 'Sign up')]",
                    "//a[contains(text(), '注册')]",
                    "//a[contains(text(), 'Register')]"
                };

                IWebElement signUpLink = null;
                foreach (var selector in signUpSelectors)
                {
                    try
                    {
                        if (selector.StartsWith("//"))
                        {
                            signUpLink = wait.Until(driver => driver.FindElement(By.XPath(selector)));
                        }
                        else
                        {
                            signUpLink = wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
                        }
                        break;
                    }
                    catch { }
                }

                if (signUpLink != null)
                {
                    signUpLink.Click();
                    await Task.Delay(_config.Settings.DelayBetweenSteps);
                    return true;
                }
                else
                {
                    // 直接导航到注册页面
                    _driver.Navigate().GoToUrl(_config.Urls.TraeSignUp);
                    await Task.Delay(_config.Settings.DelayBetweenSteps);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"导航到注册页面失败: {ex.Message}");
            }
        }

        public async Task<bool> FillRegistrationFormAsync(string email, string password)
        {
            try
            {
                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(30));

                // 查找邮箱输入框
                var emailSelectors = new[]
                {
                    "input[type='email']",
                    "input[name*='email']",
                    "input[id*='email']",
                    ".email-input",
                    "#email"
                };

                IWebElement emailInput = null;
                foreach (var selector in emailSelectors)
                {
                    try
                    {
                        emailInput = wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
                        break;
                    }
                    catch { }
                }

                if (emailInput == null)
                    throw new Exception("找不到邮箱输入框");

                // 清空并输入邮箱
                emailInput.Clear();
                await Task.Delay(500);
                emailInput.SendKeys(email);
                await Task.Delay(_config.Settings.DelayBetweenSteps);

                // 查找密码输入框
                var passwordSelectors = new[]
                {
                    "input[type='password']",
                    "input[name*='password']",
                    "input[id*='password']",
                    ".password-input",
                    "#password"
                };

                IWebElement passwordInput = null;
                foreach (var selector in passwordSelectors)
                {
                    try
                    {
                        passwordInput = _driver.FindElement(By.CssSelector(selector));
                        break;
                    }
                    catch { }
                }

                if (passwordInput != null)
                {
                    passwordInput.Clear();
                    await Task.Delay(500);
                    passwordInput.SendKeys(password);
                    await Task.Delay(_config.Settings.DelayBetweenSteps);
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"填写注册表单失败: {ex.Message}");
            }
        }

        public async Task<bool> RequestVerificationCodeAsync()
        {
            try
            {
                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(30));

                // 查找获取验证码按钮
                var codeButtonSelectors = new[]
                {
                    "button[type='button']",
                    ".get-code",
                    ".verification-code",
                    ".send-code",
                    "//button[contains(text(), 'Get Verification Code')]",
                    "//button[contains(text(), '获取验证码')]",
                    "//button[contains(text(), 'Send Code')]",
                    "//button[contains(text(), 'Verify')]"
                };

                IWebElement codeButton = null;
                foreach (var selector in codeButtonSelectors)
                {
                    try
                    {
                        if (selector.StartsWith("//"))
                        {
                            codeButton = wait.Until(driver => driver.FindElement(By.XPath(selector)));
                        }
                        else
                        {
                            codeButton = wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
                        }
                        
                        if (codeButton.Enabled && codeButton.Displayed)
                            break;
                        else
                            codeButton = null;
                    }
                    catch { }
                }

                if (codeButton == null)
                    throw new Exception("找不到获取验证码按钮");

                // 点击获取验证码按钮
                codeButton.Click();
                await Task.Delay(_config.Settings.DelayBetweenSteps);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"请求验证码失败: {ex.Message}");
            }
        }

        public async Task<bool> EnterVerificationCodeAsync(string code)
        {
            try
            {
                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(30));

                // 查找验证码输入框
                var codeInputSelectors = new[]
                {
                    "input[name*='code']",
                    "input[id*='code']",
                    "input[placeholder*='code']",
                    "input[placeholder*='验证码']",
                    ".verification-code-input",
                    ".code-input"
                };

                IWebElement codeInput = null;
                foreach (var selector in codeInputSelectors)
                {
                    try
                    {
                        codeInput = wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
                        break;
                    }
                    catch { }
                }

                if (codeInput == null)
                    throw new Exception("找不到验证码输入框");

                // 输入验证码
                codeInput.Clear();
                await Task.Delay(500);
                codeInput.SendKeys(code);
                await Task.Delay(_config.Settings.DelayBetweenSteps);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"输入验证码失败: {ex.Message}");
            }
        }

        public async Task<bool> SubmitRegistrationAsync()
        {
            try
            {
                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(30));

                // 查找提交按钮
                var submitSelectors = new[]
                {
                    "button[type='submit']",
                    "input[type='submit']",
                    ".submit-btn",
                    ".register-btn",
                    ".signup-btn",
                    "//button[contains(text(), 'Sign Up')]",
                    "//button[contains(text(), '注册')]",
                    "//button[contains(text(), 'Register')]",
                    "//button[contains(text(), 'Submit')]"
                };

                IWebElement submitButton = null;
                foreach (var selector in submitSelectors)
                {
                    try
                    {
                        if (selector.StartsWith("//"))
                        {
                            submitButton = wait.Until(driver => driver.FindElement(By.XPath(selector)));
                        }
                        else
                        {
                            submitButton = wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
                        }
                        
                        if (submitButton.Enabled && submitButton.Displayed)
                            break;
                        else
                            submitButton = null;
                    }
                    catch { }
                }

                if (submitButton == null)
                    throw new Exception("找不到提交按钮");

                // 点击提交按钮
                submitButton.Click();
                await Task.Delay(_config.Settings.DelayBetweenSteps * 2);

                // 检查是否注册成功
                return await CheckRegistrationSuccessAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"提交注册失败: {ex.Message}");
            }
        }

        private async Task<bool> CheckRegistrationSuccessAsync()
        {
            try
            {
                await Task.Delay(3000);

                // 检查成功指示器
                var successIndicators = new[]
                {
                    ".success",
                    ".registered",
                    ".welcome",
                    "//div[contains(text(), 'success')]",
                    "//div[contains(text(), '成功')]",
                    "//div[contains(text(), 'welcome')]"
                };

                foreach (var indicator in successIndicators)
                {
                    try
                    {
                        IWebElement element;
                        if (indicator.StartsWith("//"))
                        {
                            element = _driver.FindElement(By.XPath(indicator));
                        }
                        else
                        {
                            element = _driver.FindElement(By.CssSelector(indicator));
                        }

                        if (element.Displayed)
                            return true;
                    }
                    catch { }
                }

                // 检查URL变化
                var currentUrl = _driver.Url;
                if (currentUrl.Contains("dashboard") || currentUrl.Contains("home") || 
                    currentUrl.Contains("welcome") || currentUrl.Contains("success"))
                {
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        private void InitializeBrowser()
        {
            if (_driver != null)
                return;

            var options = new ChromeOptions();

            // 基本配置
            options.AddArgument("--no-sandbox");
            options.AddArgument("--disable-dev-shm-usage");
            options.AddArgument("--disable-gpu");
            options.AddArgument("--disable-web-security");
            options.AddArgument("--disable-features=VizDisplayCompositor");
            options.AddArgument("--disable-extensions");
            options.AddArgument("--disable-plugins");
            options.AddArgument("--window-size=1920,1080");
            options.AddArgument("--start-maximized");

            // 禁用日志
            options.AddArgument("--log-level=3");
            options.AddArgument("--silent");
            options.AddArgument("--disable-logging");
            options.AddArgument("--disable-dev-tools");

            // 性能优化
            options.AddArgument("--memory-pressure-off");
            options.AddArgument("--max_old_space_size=4096");

            if (_config.Settings.BrowserHeadless)
            {
                options.AddArgument("--headless=new");
            }

            // 自动化隐藏
            options.AddArgument("--disable-blink-features=AutomationControlled");
            options.AddExcludedArgument("enable-automation");
            options.AddAdditionalOption("useAutomationExtension", false);

            // 随机User-Agent
            var userAgent = _config.UserAgents[_random.Next(_config.UserAgents.Count)];
            options.AddArgument($"--user-agent={userAgent}");

            // 设置Chrome服务
            var service = ChromeDriverService.CreateDefaultService();
            service.HideCommandPromptWindow = true;
            service.SuppressInitialDiagnosticInformation = true;

            try
            {
                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(120));
                _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(20);
                _driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(120);

                // 执行脚本隐藏自动化特征
                try
                {
                    ((IJavaScriptExecutor)_driver).ExecuteScript("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})");
                }
                catch
                {
                    // 忽略脚本执行错误
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"初始化浏览器失败: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _driver?.Quit();
            _driver?.Dispose();
        }
    }
}
