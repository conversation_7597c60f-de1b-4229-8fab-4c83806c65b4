{"format": 1, "restore": {"E:\\111\\TraeAutoRegister\\TraeAutoRegister.csproj": {}}, "projects": {"E:\\111\\TraeAutoRegister\\TraeAutoRegister.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\111\\TraeAutoRegister\\TraeAutoRegister.csproj", "projectName": "TraeAutoRegister", "projectPath": "E:\\111\\TraeAutoRegister\\TraeAutoRegister.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\111\\TraeAutoRegister\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[7.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Selenium.WebDriver": {"target": "Package", "version": "[4.15.0, )"}, "Selenium.WebDriver.ChromeDriver": {"target": "Package", "version": "[119.0.6045.10500, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}