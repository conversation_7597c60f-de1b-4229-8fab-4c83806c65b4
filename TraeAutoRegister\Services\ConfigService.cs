using System;
using System.IO;
using Newtonsoft.Json;
using TraeAutoRegister.Models;

namespace TraeAutoRegister.Services
{
    public class ConfigService
    {
        private const string ConfigFileName = "config.json";
        private AppConfig _config;

        public AppConfig LoadConfig()
        {
            if (_config != null)
                return _config;

            try
            {
                if (File.Exists(ConfigFileName))
                {
                    var json = File.ReadAllText(ConfigFileName);
                    _config = JsonConvert.DeserializeObject<AppConfig>(json);

                    // 确保反序列化后的对象不为空
                    if (_config == null)
                    {
                        System.Diagnostics.Debug.WriteLine("JSON反序列化返回null，使用默认配置");
                        _config = CreateDefaultConfig();
                    }

                    // 确保Settings不为空
                    if (_config.Settings == null)
                    {
                        System.Diagnostics.Debug.WriteLine("Settings为null，创建默认Settings");
                        _config.Settings = new Models.Settings();
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("配置文件不存在，创建默认配置");
                    _config = CreateDefaultConfig();
                    SaveConfig(_config);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载配置失败: {ex.Message}，使用默认配置");
                _config = CreateDefaultConfig();
            }

            return _config;
        }

        public void SaveConfig(AppConfig config)
        {
            try
            {
                var json = JsonConvert.SerializeObject(config, Formatting.Indented);
                File.WriteAllText(ConfigFileName, json);
                _config = config;
            }
            catch (Exception ex)
            {
                throw new Exception($"保存配置文件失败: {ex.Message}");
            }
        }

        private AppConfig CreateDefaultConfig()
        {
            return new AppConfig
            {
                Settings = new Models.Settings
                {
                    DefaultPassword = "AAAaaa111",
                    DelayBetweenSteps = 2000,
                    EmailCheckInterval = 5000,
                    MaxEmailWaitTime = 300000,
                    MaxRetryAttempts = 3,
                    BrowserHeadless = false,
                    BrowserTimeout = 30000,
                    SaveAccountsToFile = true,
                    AccountsFileName = "registered_accounts.txt"
                },
                Urls = new Models.Urls
                {
                    TraeLogin = "https://www.trae.ai/login",
                    TraeSignUp = "https://www.trae.ai/sign-up",
                    TemporamEmail = "https://www.temporam.com/zh/ems"
                },
                EmailDomains = new System.Collections.Generic.List<string>
                {
                    "@mona.edu.kg"
                },
                UserAgents = new System.Collections.Generic.List<string>
                {
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/119.0"
                }
            };
        }
    }
}
