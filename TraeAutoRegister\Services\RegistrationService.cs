using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using TraeAutoRegister.Models;

namespace TraeAutoRegister.Services
{
    public class RegistrationService : IDisposable
    {
        private readonly ConfigService _configService;
        private readonly TempEmailService _emailService;
        private readonly WebAutomationService _webService;
        private readonly AppConfig _config;

        private readonly ApiRegistrationService _apiService;
        private readonly bool _useApiMode;

        public RegistrationService(ConfigService configService, bool useApiMode = true)
        {
            _configService = configService;
            _config = configService.LoadConfig();
            _useApiMode = useApiMode;

            if (_useApiMode)
            {
                _apiService = new ApiRegistrationService(configService);
            }
            else
            {
                _emailService = new TempEmailService();
                _webService = new WebAutomationService(_config);
            }
        }

        public async Task<RegistrationResult> RegisterAccountAsync(CancellationToken cancellationToken = default)
        {
            var result = new RegistrationResult();
            var account = new AccountInfo
            {
                Password = _config.Settings.DefaultPassword,
                CreatedAt = DateTime.Now,
                Status = "开始注册"
            };

            try
            {
                // 步骤1: 生成临时邮箱
                account.Status = "生成临时邮箱";
                account.Email = await _emailService.GenerateEmailAsync();
                
                if (string.IsNullOrEmpty(account.Email))
                {
                    throw new Exception("生成临时邮箱失败");
                }

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤2: 导航到注册页面
                account.Status = "导航到注册页面";
                var navigateSuccess = await _webService.NavigateToSignUpPageAsync();
                if (!navigateSuccess)
                {
                    throw new Exception("导航到注册页面失败");
                }

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤3: 填写注册表单
                account.Status = "填写注册表单";
                var fillSuccess = await _webService.FillRegistrationFormAsync(account.Email, account.Password);
                if (!fillSuccess)
                {
                    throw new Exception("填写注册表单失败");
                }

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤4: 请求验证码
                account.Status = "请求验证码";
                var requestCodeSuccess = await _webService.RequestVerificationCodeAsync();
                if (!requestCodeSuccess)
                {
                    throw new Exception("请求验证码失败");
                }

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤5: 等待并获取验证码
                account.Status = "等待验证码";
                var verificationCode = await _emailService.WaitForVerificationCodeAsync(_config.Settings.MaxEmailWaitTime);
                
                if (string.IsNullOrEmpty(verificationCode))
                {
                    throw new Exception("获取验证码失败或超时");
                }

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤6: 输入验证码
                account.Status = "输入验证码";
                var enterCodeSuccess = await _webService.EnterVerificationCodeAsync(verificationCode);
                if (!enterCodeSuccess)
                {
                    throw new Exception("输入验证码失败");
                }

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤7: 提交注册
                account.Status = "提交注册";
                var submitSuccess = await _webService.SubmitRegistrationAsync();
                if (!submitSuccess)
                {
                    throw new Exception("提交注册失败");
                }

                // 注册成功
                account.Status = "注册成功";
                account.IsVerified = true;
                result.Success = true;
                result.Message = "账号注册成功";
                result.Account = account;

                // 保存账号信息到文件
                if (_config.Settings.SaveAccountsToFile)
                {
                    await SaveAccountToFileAsync(account);
                }
            }
            catch (OperationCanceledException)
            {
                account.Status = "用户取消";
                result.Success = false;
                result.Message = "注册过程被用户取消";
                result.Account = account;
                throw;
            }
            catch (Exception ex)
            {
                account.Status = "注册失败";
                result.Success = false;
                result.Message = ex.Message;
                result.Exception = ex;
                result.Account = account;
            }

            return result;
        }

        private async Task SaveAccountToFileAsync(AccountInfo account)
        {
            try
            {
                var fileName = _config.Settings.AccountsFileName;
                var accountInfo = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss}\t{account.Email}\t{account.Password}\t{account.Status}\n";
                
                await File.AppendAllTextAsync(fileName, accountInfo);
            }
            catch (Exception ex)
            {
                // 记录错误但不影响主流程
                Console.WriteLine($"保存账号信息失败: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _emailService?.Dispose();
            _webService?.Dispose();
        }
    }
}
