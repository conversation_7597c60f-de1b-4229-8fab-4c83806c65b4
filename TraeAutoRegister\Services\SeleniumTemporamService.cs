using System;
using System.Threading;
using System.Threading.Tasks;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using System.Text.RegularExpressions;

namespace TraeAutoRegister.Services
{
    public class SeleniumTemporamService : IDisposable
    {
        private IWebDriver _driver;
        private WebDriverWait _wait;
        private readonly string _temporamUrl = "https://www.temporam.com/zh/ems";

        public async Task<string> GetEducationEmailAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                InitializeDriver();
                
                // 访问Temporam网站
                _driver.Navigate().GoToUrl(_temporamUrl);
                
                // 等待页面加载
                await Task.Delay(3000, cancellationToken);
                
                // 尝试点击教育邮箱标签
                try
                {
                    var eduTab = _wait.Until(d => d.FindElement(By.XPath("//button[contains(text(), '教育')]")));
                    eduTab.Click();
                    await Task.Delay(2000, cancellationToken);
                }
                catch
                {
                    // 如果没有教育标签，继续使用默认的
                }
                
                // 等待邮箱生成并显示
                string email = null;
                var maxAttempts = 30; // 最多等待30秒
                
                for (int i = 0; i < maxAttempts; i++)
                {
                    try
                    {
                        // 尝试多种可能的邮箱显示元素
                        var emailSelectors = new[]
                        {
                            "//div[contains(@class, 'email')]//text()[contains(., '@')]",
                            "//input[contains(@value, '@')]",
                            "//span[contains(text(), '@')]",
                            "//div[contains(text(), '@')]",
                            "//*[contains(text(), '@') and contains(text(), '.edu')]",
                            "//*[contains(text(), '@') and contains(text(), '.kg')]"
                        };
                        
                        foreach (var selector in emailSelectors)
                        {
                            try
                            {
                                var element = _driver.FindElement(By.XPath(selector));
                                var text = element.GetAttribute("value") ?? element.Text;
                                
                                if (!string.IsNullOrEmpty(text) && text.Contains("@"))
                                {
                                    // 使用正则表达式提取邮箱
                                    var emailMatch = Regex.Match(text, @"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}");
                                    if (emailMatch.Success)
                                    {
                                        email = emailMatch.Value;
                                        break;
                                    }
                                }
                            }
                            catch
                            {
                                continue;
                            }
                        }
                        
                        if (!string.IsNullOrEmpty(email))
                        {
                            break;
                        }
                        
                        // 如果还没找到邮箱，尝试刷新或重新生成
                        if (i == 10 || i == 20)
                        {
                            try
                            {
                                // 尝试点击刷新或生成按钮
                                var refreshButtons = _driver.FindElements(By.XPath("//button[contains(text(), '刷新') or contains(text(), '生成') or contains(text(), '复制')]"));
                                if (refreshButtons.Count > 0)
                                {
                                    refreshButtons[0].Click();
                                    await Task.Delay(2000, cancellationToken);
                                }
                            }
                            catch
                            {
                                // 忽略刷新错误
                            }
                        }
                        
                        await Task.Delay(1000, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch
                    {
                        await Task.Delay(1000, cancellationToken);
                    }
                }
                
                if (string.IsNullOrEmpty(email))
                {
                    throw new Exception("在30秒内未能获取到邮箱地址");
                }
                
                return email;
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new Exception($"Selenium获取邮箱失败: {ex.Message}");
            }
        }

        private void InitializeDriver()
        {
            if (_driver != null)
                return;

            var options = new ChromeOptions();
            options.AddArgument("--headless"); // 无头模式，不显示浏览器窗口
            options.AddArgument("--no-sandbox");
            options.AddArgument("--disable-dev-shm-usage");
            options.AddArgument("--disable-gpu");
            options.AddArgument("--window-size=1920,1080");
            options.AddArgument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");

            try
            {
                _driver = new ChromeDriver(options);
                _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            }
            catch (Exception ex)
            {
                throw new Exception($"无法初始化Chrome浏览器: {ex.Message}。请确保已安装Chrome浏览器。");
            }
        }

        public void Dispose()
        {
            try
            {
                _driver?.Quit();
                _driver?.Dispose();
            }
            catch
            {
                // 忽略释放错误
            }
        }
    }
}
