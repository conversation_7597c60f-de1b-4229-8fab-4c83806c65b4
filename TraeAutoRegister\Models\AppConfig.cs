using System;
using System.Collections.Generic;

namespace TraeAutoRegister.Models
{
    public class AppConfig
    {
        public Settings Settings { get; set; } = new Settings();
        public Urls Urls { get; set; } = new Urls();
        public List<string> EmailDomains { get; set; } = new List<string>();
        public List<string> UserAgents { get; set; } = new List<string>();
    }

    public class Settings
    {
        public string DefaultPassword { get; set; } = "AAAaaa111";
        public int DelayBetweenSteps { get; set; } = 2000;
        public int EmailCheckInterval { get; set; } = 5000;
        public int MaxEmailWaitTime { get; set; } = 300000;
        public int MaxRetryAttempts { get; set; } = 3;
        public bool BrowserHeadless { get; set; } = false;
        public int BrowserTimeout { get; set; } = 30000;
        public bool SaveAccountsToFile { get; set; } = true;
        public string AccountsFileName { get; set; } = "registered_accounts.txt";
    }

    public class Urls
    {
        public string TraeLogin { get; set; } = "https://www.trae.ai/login";
        public string TraeSignUp { get; set; } = "https://www.trae.ai/sign-up";
        public string TemporamEmail { get; set; } = "https://www.temporam.com/zh/ems";
    }

    public class RegistrationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public AccountInfo Account { get; set; }
        public Exception Exception { get; set; }
    }
}
