using System;
using System.Threading;
using System.Threading.Tasks;
using TraeAutoRegister.Models;

namespace TraeAutoRegister.Services
{
    public class SimpleApiRegistrationService : IDisposable
    {
        private readonly ConfigService _configService;
        private readonly AppConfig _config;
        private Random _random;

        public SimpleApiRegistrationService(ConfigService configService)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始初始化SimpleApiRegistrationService");

                _configService = configService ?? throw new ArgumentNullException(nameof(configService));
                System.Diagnostics.Debug.WriteLine("ConfigService检查通过");

                _config = configService.LoadConfig();
                System.Diagnostics.Debug.WriteLine($"配置加载完成: {_config != null}");

                _random = new Random();
                System.Diagnostics.Debug.WriteLine("Random对象创建完成");

                // 检查配置是否正确加载
                if (_config == null)
                {
                    System.Diagnostics.Debug.WriteLine("警告: 配置加载失败，使用默认配置");
                    _config = new AppConfig();
                }

                if (_config.Settings == null)
                {
                    System.Diagnostics.Debug.WriteLine("警告: Settings为空，创建默认Settings");
                    _config.Settings = new Models.Settings();
                }

                System.Diagnostics.Debug.WriteLine($"SimpleApiRegistrationService 初始化完成，密码: {_config.Settings.DefaultPassword}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<RegistrationResult> RegisterAccountAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始简化API注册流程");

                // 检查配置
                System.Diagnostics.Debug.WriteLine($"配置对象: {_config != null}");
                System.Diagnostics.Debug.WriteLine($"Settings对象: {_config?.Settings != null}");
                System.Diagnostics.Debug.WriteLine($"默认密码: {_config?.Settings?.DefaultPassword ?? "null"}");

                // 模拟注册过程的各个步骤
                await SimulateRegistrationStep("生成教育邮箱", 1000, cancellationToken);
                var testEmail = GenerateEducationEmail();
                System.Diagnostics.Debug.WriteLine($"生成的邮箱: {testEmail}");
                
                await SimulateRegistrationStep("获取TRAE注册页面", 800, cancellationToken);
                await SimulateRegistrationStep("提交教育邮箱注册数据", 1200, cancellationToken);
                await SimulateRegistrationStep("等待教育邮箱验证邮件", 1500, cancellationToken);
                await SimulateRegistrationStep("完成教育邮箱验证", 800, cancellationToken);
                
                var account = new AccountInfo
                {
                    Email = testEmail,
                    Password = _config?.Settings?.DefaultPassword ?? "AAAaaa111",
                    CreatedAt = DateTime.Now,
                    Status = "教育邮箱注册成功"
                };

                System.Diagnostics.Debug.WriteLine($"教育邮箱注册成功: {testEmail}");

                return new RegistrationResult
                {
                    Success = true,
                    Account = account,
                    Message = "教育邮箱注册成功"
                };
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("注册被取消");
                return new RegistrationResult
                {
                    Success = false,
                    Message = "注册被用户取消"
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"注册异常: {ex.Message}");
                return new RegistrationResult
                {
                    Success = false,
                    Message = $"注册过程异常: {ex.Message}"
                };
            }
        }

        private string GenerateEducationEmail()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始生成教育邮箱");

                // 检查_random对象
                if (_random == null)
                {
                    System.Diagnostics.Debug.WriteLine("_random为null，创建新的Random对象");
                    _random = new Random();
                }

                // 生成随机的教育邮箱地址
                var prefixes = new[]
                {
                    "student", "scholar", "academic", "research", "edu", "learn",
                    "study", "university", "college", "campus", "faculty"
                };

                var numbers = _random.Next(1000, 9999);
                var prefix = prefixes[_random.Next(prefixes.Length)];
                var randomSuffix = _random.Next(100, 999);

                var email = $"{prefix}{numbers}{randomSuffix}@mona.edu.kg";
                System.Diagnostics.Debug.WriteLine($"生成教育邮箱: {email}");

                return email;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成邮箱异常: {ex.Message}");
                // 返回一个固定的邮箱作为备用
                return $"student{DateTime.Now.Ticks}@mona.edu.kg";
            }
        }

        private async Task SimulateRegistrationStep(string stepName, int delayMs, CancellationToken cancellationToken)
        {
            System.Diagnostics.Debug.WriteLine($"执行步骤: {stepName}");
            await Task.Delay(delayMs, cancellationToken);

            // 随机模拟一些失败情况（20%概率，用于测试错误处理）
            if (_random.Next(100) < 20)
            {
                var errorMessages = new[]
                {
                    $"网络连接超时 - {stepName}步骤失败",
                    $"服务器返回错误代码 500 - {stepName}处理异常",
                    $"邮箱服务暂时不可用 - {stepName}无法完成",
                    $"验证码获取失败 - {stepName}需要重试",
                    $"API限流 - {stepName}请求过于频繁，请稍后再试"
                };

                var errorMessage = errorMessages[_random.Next(errorMessages.Length)];
                throw new Exception(errorMessage);
            }
        }

        public void Dispose()
        {
            // 清理资源
            System.Diagnostics.Debug.WriteLine("SimpleApiRegistrationService 已释放");
        }
    }
}
