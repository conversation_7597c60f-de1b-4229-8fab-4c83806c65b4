<Window x:Class="TraeAutoRegister.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="TRAE自动注册工具" Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">
    
    <Window.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#6366F1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5B5FE8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#4F46E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 紫色按钮样式 -->
        <Style x:Key="PurpleButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#A855F7"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#9333EA"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#7C3AED"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 现代化文本框样式 -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="#F8FAFC"/>
            <Setter Property="BorderBrush" Value="#E2E8F0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- 主容器 -->
    <Border Background="White" CornerRadius="12" Margin="0">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="4" BlurRadius="20"/>
        </Border.Effect>

        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 关闭按钮 -->
            <Button Grid.Row="0" HorizontalAlignment="Right" VerticalAlignment="Top"
                    Width="24" Height="24" Margin="0,-10,-10,0"
                    Background="Transparent" BorderThickness="0"
                    Content="✕" FontSize="12" Foreground="#94A3B8"
                    Click="CloseButton_Click"/>

            <!-- Logo区域 -->
            <Ellipse Grid.Row="1" Width="80" Height="80"
                     Fill="#FEF3C7" Margin="0,20,0,20"
                     HorizontalAlignment="Center"/>

            <TextBlock Grid.Row="1" Text="🚀" FontSize="40"
                       HorizontalAlignment="Center" VerticalAlignment="Center"
                       Margin="0,20,0,20"/>

            <!-- 标题 -->
            <TextBlock Grid.Row="2" Text="TRAE自动注册"
                       FontSize="24" FontWeight="Bold"
                       HorizontalAlignment="Center"
                       Foreground="#1E293B" Margin="0,0,0,10"/>

            <!-- 状态指示 -->
            <Border Grid.Row="3" Background="#FEF2F2" CornerRadius="20"
                    Padding="12,6" HorizontalAlignment="Center" Margin="0,0,0,30">
                <StackPanel Orientation="Horizontal">
                    <Ellipse Width="8" Height="8" Fill="#EF4444" Margin="0,0,8,0"/>
                    <TextBlock x:Name="txtStatusIndicator" Text="免费使用中"
                               FontSize="12" Foreground="#DC2626" FontWeight="Medium"/>
                </StackPanel>
            </Border>

            <!-- 数量输入 -->
            <StackPanel Grid.Row="4" Orientation="Horizontal"
                        HorizontalAlignment="Center" Margin="0,0,0,20">
                <TextBlock Text="批量数量:" FontSize="14" Foreground="#64748B"
                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBox x:Name="txtBatchCount" Text="1"
                         Style="{StaticResource ModernTextBox}"
                         Width="80" TextAlignment="Center"/>
            </StackPanel>

            <!-- 主要按钮 -->
            <Button Grid.Row="5" x:Name="btnStart"
                    Content="一键获取账号"
                    Style="{StaticResource ModernButton}"
                    Width="200" Margin="0,0,0,15"
                    Click="BtnStart_Click"/>

            <Button Grid.Row="6" x:Name="btnTutorial"
                    Content="使用教程"
                    Style="{StaticResource PurpleButton}"
                    Width="200" Margin="0,0,0,15"
                    Click="BtnTutorial_Click"/>

            <Button Grid.Row="7" x:Name="btnTestLogin"
                    Content="测试账号登录"
                    Style="{StaticResource PurpleButton}"
                    Width="200" Margin="0,0,0,15"
                    Click="BtnTestLogin_Click"/>

            <Button Grid.Row="8" x:Name="btnAccountManager"
                    Content="🏦 账号库管理"
                    Style="{StaticResource PurpleButton}"
                    Width="200" Margin="0,0,0,30"
                    Click="BtnAccountManager_Click"/>

            <!-- 版本信息 -->
            <TextBlock Grid.Row="9" Text="↓ v2.3.0"
                       FontSize="12" Foreground="#94A3B8"
                       HorizontalAlignment="Center"/>

            <!-- 隐藏的进度和状态信息 -->
            <Grid x:Name="StatusPanel" Grid.Row="10" Visibility="Collapsed" Margin="0,20,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" x:Name="lblCurrentStep"
                           Text="当前状态: 等待开始"
                           FontSize="12" Foreground="#64748B"
                           HorizontalAlignment="Center" Margin="0,0,0,10"/>

                <ProgressBar Grid.Row="1" x:Name="progressBar"
                             Height="6" Margin="0,0,0,10"
                             Background="#F1F5F9" Foreground="#6366F1"
                             Minimum="0" Maximum="100" Value="0"/>

                <StackPanel Grid.Row="2" Orientation="Horizontal"
                            HorizontalAlignment="Center">
                    <TextBlock x:Name="lblSuccessCount" Text="成功: 0"
                               FontSize="12" Foreground="#10B981" Margin="0,0,15,0"/>
                    <TextBlock x:Name="lblFailedCount" Text="失败: 0"
                               FontSize="12" Foreground="#EF4444" Margin="0,0,15,0"/>
                    <TextBlock x:Name="lblTotalCount" Text="总计: 0"
                               FontSize="12" Foreground="#6366F1"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window>
