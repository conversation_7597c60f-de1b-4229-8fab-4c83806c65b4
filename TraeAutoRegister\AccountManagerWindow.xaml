<Window x:Class="TraeAutoRegister.AccountManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="TRAE账号库管理" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        WindowStyle="SingleBorderWindow">
    
    <Window.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#6366F1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5B5FE8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#4F46E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 绿色按钮样式 -->
        <Style x:Key="GreenButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#10B981"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#059669"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#047857"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 红色按钮样式 -->
        <Style x:Key="RedButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#EF4444"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#DC2626"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#B91C1C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和统计信息 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="🏦 TRAE账号库管理" FontSize="24" FontWeight="Bold" 
                       Foreground="#1E293B" HorizontalAlignment="Center" Margin="0,0,0,10"/>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock x:Name="lblTotalAccounts" Text="总账号: 0" 
                           FontSize="14" Foreground="#64748B" Margin="0,0,20,0"/>
                <TextBlock x:Name="lblValidAccounts" Text="可用: 0" 
                           FontSize="14" Foreground="#10B981" Margin="0,0,20,0"/>
                <TextBlock x:Name="lblInvalidAccounts" Text="异常: 0" 
                           FontSize="14" Foreground="#EF4444"/>
            </StackPanel>
        </StackPanel>

        <!-- 账号列表 -->
        <Border Grid.Row="1" BorderBrush="#E2E8F0" BorderThickness="1" CornerRadius="8">
            <DataGrid x:Name="dgAccounts" 
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      SelectionMode="Single"
                      Background="White"
                      RowBackground="White"
                      AlternatingRowBackground="#F8FAFC">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="邮箱" Binding="{Binding Email}" Width="*" IsReadOnly="True"/>
                    <DataGridTextColumn Header="密码" Binding="{Binding Password}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="注册时间" Binding="{Binding RegisterTime}" Width="150" IsReadOnly="True"/>
                    <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80" IsReadOnly="True"/>
                    <DataGridTemplateColumn Header="操作" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="🌐 浏览器登录" 
                                            Style="{StaticResource GreenButton}"
                                            Margin="2" Padding="8,4"
                                            Click="BtnBrowserLogin_Click"
                                            Tag="{Binding}"/>
                                    <Button Content="🧪 测试" 
                                            Style="{StaticResource ModernButton}"
                                            Margin="2" Padding="8,4"
                                            Click="BtnTestAccount_Click"
                                            Tag="{Binding}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="btnRefresh" Content="🔄 刷新列表" 
                    Style="{StaticResource ModernButton}" 
                    Margin="0,0,10,0" Click="BtnRefresh_Click"/>
            <Button x:Name="btnTestAll" Content="🧪 批量测试" 
                    Style="{StaticResource ModernButton}" 
                    Margin="0,0,10,0" Click="BtnTestAll_Click"/>
            <Button x:Name="btnExport" Content="📤 导出账号" 
                    Style="{StaticResource ModernButton}" 
                    Margin="0,0,10,0" Click="BtnExport_Click"/>
            <Button x:Name="btnClose" Content="❌ 关闭" 
                    Style="{StaticResource RedButton}" 
                    Click="BtnClose_Click"/>
        </StackPanel>
    </Grid>
</Window>
