# TRAE自动注册工具

一个用于自动注册TRAE账号的Windows桌面应用程序，使用临时邮箱服务进行邮箱验证。

## 功能特性

- 🖥️ **图形化界面**: 简洁易用的WPF界面
- 📧 **临时邮箱**: 自动生成和管理临时邮箱地址
- 🤖 **全自动化**: 完整的注册流程自动化
- 📊 **批量处理**: 支持批量注册多个账号
- 📝 **详细日志**: 实时显示注册进度和状态
- 💾 **数据保存**: 自动保存成功注册的账号信息
- ⚙️ **配置灵活**: 支持自定义配置参数

## 系统要求

- Windows 10/11 (64位)
- .NET 6.0 Runtime (如果使用独立版本则不需要)
- Chrome浏览器 (用于Web自动化)
- 网络连接

## 安装说明

### 方式一：使用预编译版本
1. 下载 `dist` 文件夹中的所有文件
2. 双击 `TraeAutoRegister.exe` 运行程序

### 方式二：从源码编译
1. 安装 .NET 6.0 SDK
2. 运行 `build.bat` 编译脚本
3. 编译完成后在 `dist` 文件夹中找到可执行文件

## 使用方法

### 基本操作
1. **启动程序**: 双击 `TraeAutoRegister.exe`
2. **设置数量**: 在"批量数量"输入框中输入要注册的账号数量
3. **配置选项**: 
   - 勾选"无头模式运行"可以隐藏浏览器窗口
   - 勾选"保存账号信息到文件"会将成功的账号保存到文本文件
4. **开始注册**: 点击"开始注册"按钮
5. **监控进度**: 在日志区域查看实时进度和状态
6. **停止注册**: 如需中断，点击"停止"按钮

### 注册流程
程序会自动执行以下步骤：
1. 生成临时邮箱地址
2. 导航到TRAE注册页面
3. 填写邮箱和密码信息
4. 请求邮箱验证码
5. 监控临时邮箱获取验证码
6. 输入验证码完成注册

### 配置文件说明
`config.json` 文件包含以下可配置项：

```json
{
  "Settings": {
    "DefaultPassword": "AAAaaa111",     // 默认密码
    "DelayBetweenSteps": 2000,          // 步骤间延迟(毫秒)
    "EmailCheckInterval": 5000,         // 邮件检查间隔(毫秒)
    "MaxEmailWaitTime": 300000,         // 最大邮件等待时间(毫秒)
    "MaxRetryAttempts": 3,              // 最大重试次数
    "BrowserHeadless": false,           // 是否无头模式
    "BrowserTimeout": 30000,            // 浏览器超时时间(毫秒)
    "SaveAccountsToFile": true,         // 是否保存账号到文件
    "AccountsFileName": "registered_accounts.txt"  // 账号文件名
  }
}
```

## 输出文件

### 账号信息文件
成功注册的账号信息会保存到 `registered_accounts.txt` 文件中，格式如下：
```
创建时间	邮箱地址	密码	状态	验证状态
2024-01-01 12:00:00	<EMAIL>	AAAaaa111	注册成功	已验证
```

### 日志文件
详细的运行日志会保存到 `logs/` 文件夹中，按日期分文件存储。

## 故障排除

### 常见问题

1. **程序无法启动**
   - 确保已安装.NET 6.0 Runtime
   - 检查Windows版本是否支持
   - 以管理员身份运行

2. **浏览器相关错误**
   - 确保已安装Chrome浏览器
   - 检查ChromeDriver版本是否匹配
   - 关闭其他占用Chrome的程序

3. **网络连接问题**
   - 检查网络连接是否正常
   - 确认可以访问TRAE和临时邮箱网站
   - 检查防火墙设置

4. **验证码获取失败**
   - 临时邮箱服务可能不稳定，可以重试
   - 检查邮件检查间隔设置
   - 增加最大等待时间

### 调试模式
- 取消勾选"无头模式运行"可以看到浏览器操作过程
- 查看日志区域的详细信息
- 检查配置文件设置是否正确

## 注意事项

⚠️ **重要提醒**:
- 本工具仅供学习和研究目的使用
- 请遵守TRAE网站的服务条款和使用协议
- 不要过于频繁地注册账号，避免被网站检测和封禁
- 建议在注册间隔中增加适当的延迟时间
- 请合法合规地使用本工具

## 技术架构

- **界面框架**: WPF (Windows Presentation Foundation)
- **Web自动化**: Selenium WebDriver
- **HTTP请求**: HttpClient
- **JSON处理**: Newtonsoft.Json
- **日志记录**: Microsoft.Extensions.Logging

## 版本历史

- **v1.0.0**: 初始版本，支持基本的自动注册功能

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue报告问题
- 提交Pull Request贡献代码

---

**免责声明**: 使用本工具产生的任何后果由用户自行承担，开发者不承担任何责任。
