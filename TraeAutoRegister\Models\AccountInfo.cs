using System;
using System.ComponentModel;

namespace TraeAutoRegister.Models
{
    public class AccountInfo : INotifyPropertyChanged
    {
        private string _email;
        private string _password;
        private DateTime _registerTime;
        private string _status;
        private string _note;
        private DateTime _createdAt;
        private bool _isVerified;

        public string Email
        {
            get => _email;
            set
            {
                _email = value;
                OnPropertyChanged(nameof(Email));
            }
        }

        public string Password
        {
            get => _password;
            set
            {
                _password = value;
                OnPropertyChanged(nameof(Password));
            }
        }

        public DateTime RegisterTime
        {
            get => _registerTime;
            set
            {
                _registerTime = value;
                OnPropertyChanged(nameof(RegisterTime));
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        public string Note
        {
            get => _note;
            set
            {
                _note = value;
                OnPropertyChanged(nameof(Note));
            }
        }

        // 为了兼容性添加的属性
        public DateTime CreatedAt
        {
            get => _createdAt != default ? _createdAt : _registerTime;
            set
            {
                _createdAt = value;
                OnPropertyChanged(nameof(CreatedAt));
            }
        }

        public bool IsVerified
        {
            get => _isVerified;
            set
            {
                _isVerified = value;
                OnPropertyChanged(nameof(IsVerified));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public override string ToString()
        {
            return $"{Email} - {Status}";
        }
    }
}
