using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using TraeAutoRegister.Models;
using TraeAutoRegister.Services;
using Microsoft.Win32;
using System.Text;

namespace TraeAutoRegister
{
    public partial class AccountManagerWindow : Window
    {
        private readonly ConfigService _configService;
        private readonly BrowserLoginService _browserLoginService;
        private ObservableCollection<AccountInfo> _accounts;

        public AccountManagerWindow(ConfigService configService)
        {
            InitializeComponent();
            _configService = configService;
            _browserLoginService = new BrowserLoginService();
            _accounts = new ObservableCollection<AccountInfo>();
            
            dgAccounts.ItemsSource = _accounts;
            
            // 订阅日志事件
            _browserLoginService.LogUpdated += OnLogUpdated;
            
            // 加载账号数据
            LoadAccounts();
        }

        private void OnLogUpdated(string message)
        {
            // 可以在这里显示日志，或者输出到控制台
            Console.WriteLine(message);
        }

        private async void LoadAccounts()
        {
            try
            {
                _accounts.Clear();
                
                var accountsFile = "registered_accounts.txt";
                if (!File.Exists(accountsFile))
                {
                    UpdateStatistics();
                    return;
                }

                var lines = await File.ReadAllLinesAsync(accountsFile);
                
                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;
                    
                    var account = ParseAccountLine(line);
                    if (account != null)
                    {
                        _accounts.Add(account);
                    }
                }
                
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载账号数据失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private AccountInfo ParseAccountLine(string line)
        {
            try
            {
                // 格式: email:password - 时间 - 状态
                var parts = line.Split(new[] { " - " }, StringSplitOptions.None);
                if (parts.Length < 2) return null;

                var emailPassword = parts[0].Split(':');
                if (emailPassword.Length < 2) return null;

                var email = emailPassword[0].Trim();
                var password = emailPassword[1].Trim();
                
                DateTime registerTime = DateTime.Now;
                if (parts.Length > 1 && DateTime.TryParse(parts[1], out var parsedTime))
                {
                    registerTime = parsedTime;
                }

                var status = parts.Length > 2 ? parts[2] : "未知";

                return new AccountInfo
                {
                    Email = email,
                    Password = password,
                    RegisterTime = registerTime,
                    Status = status
                };
            }
            catch
            {
                return null;
            }
        }

        private void UpdateStatistics()
        {
            var total = _accounts.Count;
            var valid = _accounts.Count(a => a.Status.Contains("成功") || a.Status.Contains("可用"));
            var invalid = total - valid;

            lblTotalAccounts.Text = $"总账号: {total}";
            lblValidAccounts.Text = $"可用: {valid}";
            lblInvalidAccounts.Text = $"异常: {invalid}";
        }

        private async void BtnBrowserLogin_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var account = button?.Tag as AccountInfo;
                
                if (account == null)
                {
                    MessageBox.Show("无法获取账号信息", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 确认对话框
                var result = MessageBox.Show(
                    $"将在浏览器中自动登录:\n\n邮箱: {account.Email}\n密码: {account.Password}\n\n是否继续？",
                    "确认浏览器登录", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;

                // 禁用按钮
                button.IsEnabled = false;
                button.Content = "🔄 登录中...";

                try
                {
                    // 使用简单的浏览器登录方法
                    var success = await _browserLoginService.SimpleLoginWithBrowserAsync(account.Email, account.Password);

                    if (success)
                    {
                        MessageBox.Show($"✅ 浏览器已打开TRAE登录页面\n\n📧 邮箱: {account.Email} (已复制到剪贴板)\n🔐 密码: {account.Password}\n\n💡 请在浏览器中粘贴邮箱并输入密码完成登录",
                            "浏览器登录", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("❌ 打开浏览器失败",
                            "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                finally
                {
                    // 恢复按钮
                    button.IsEnabled = true;
                    button.Content = "🌐 浏览器登录";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"浏览器登录失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnTestAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var account = button?.Tag as AccountInfo;
                
                if (account == null) return;

                // 禁用按钮
                button.IsEnabled = false;
                button.Content = "🔄 测试中...";

                try
                {
                    // 使用API测试登录
                    using var apiService = new PureRealApiRegistrationService(_configService);
                    var success = await apiService.TestLoginAsync(account.Email, account.Password);
                    
                    // 更新状态
                    account.Status = success ? "✅ 可用" : "❌ 异常";
                    UpdateStatistics();
                    
                    var message = success ? "账号测试成功，可以正常使用" : "账号测试失败，可能需要邮箱验证";
                    MessageBox.Show(message, "测试结果", MessageBoxButton.OK, 
                        success ? MessageBoxImage.Information : MessageBoxImage.Warning);
                }
                finally
                {
                    // 恢复按钮
                    button.IsEnabled = true;
                    button.Content = "🧪 测试";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试账号失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadAccounts();
        }

        private async void BtnTestAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show($"将测试所有 {_accounts.Count} 个账号，这可能需要一些时间。是否继续？",
                    "确认批量测试", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result != MessageBoxResult.Yes) return;

                btnTestAll.IsEnabled = false;
                btnTestAll.Content = "🔄 测试中...";

                var successCount = 0;
                var totalCount = _accounts.Count;

                using var apiService = new PureRealApiRegistrationService(_configService);
                
                for (int i = 0; i < _accounts.Count; i++)
                {
                    var account = _accounts[i];
                    btnTestAll.Content = $"🔄 测试中... ({i + 1}/{totalCount})";
                    
                    try
                    {
                        var success = await apiService.TestLoginAsync(account.Email, account.Password);
                        account.Status = success ? "✅ 可用" : "❌ 异常";
                        if (success) successCount++;
                    }
                    catch
                    {
                        account.Status = "❌ 异常";
                    }
                    
                    // 避免请求过于频繁
                    await Task.Delay(1000);
                }

                UpdateStatistics();
                MessageBox.Show($"批量测试完成！\n\n总计: {totalCount}\n可用: {successCount}\n异常: {totalCount - successCount}",
                    "测试完成", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"批量测试失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnTestAll.IsEnabled = true;
                btnTestAll.Content = "🧪 批量测试";
            }
        }

        private void BtnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "文本文件 (*.txt)|*.txt|CSV文件 (*.csv)|*.csv",
                    DefaultExt = "txt",
                    FileName = $"TRAE账号导出_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var content = new StringBuilder();
                    content.AppendLine("邮箱,密码,注册时间,状态");
                    
                    foreach (var account in _accounts)
                    {
                        content.AppendLine($"{account.Email},{account.Password},{account.RegisterTime:yyyy-MM-dd HH:mm:ss},{account.Status}");
                    }

                    File.WriteAllText(saveDialog.FileName, content.ToString(), Encoding.UTF8);
                    MessageBox.Show($"账号数据已导出到:\n{saveDialog.FileName}", "导出成功", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
