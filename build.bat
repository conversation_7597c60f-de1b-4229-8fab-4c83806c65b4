@echo off
echo ========================================
echo TRAE自动注册工具 - 编译脚本
echo ========================================

:: 检查.NET SDK是否安装
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到.NET SDK，请先安装.NET 6.0 SDK
    echo 下载地址: https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo 正在检查.NET SDK版本...
dotnet --version

:: 进入项目目录
cd TraeAutoRegister

:: 清理之前的构建
echo 正在清理之前的构建...
dotnet clean

:: 还原NuGet包
echo 正在还原NuGet包...
dotnet restore

:: 编译项目
echo 正在编译项目...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

:: 发布单文件可执行程序
echo 正在发布单文件可执行程序...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ../dist /p:PublishSingleFile=true

if %errorlevel% neq 0 (
    echo 发布失败！
    pause
    exit /b 1
)

cd ..

:: 复制配置文件
echo 正在复制配置文件...
copy TraeAutoRegister\config.json dist\

:: 创建启动脚本
echo 正在创建启动脚本...
echo @echo off > dist\start.bat
echo echo 启动TRAE自动注册工具... >> dist\start.bat
echo TraeAutoRegister.exe >> dist\start.bat
echo pause >> dist\start.bat

echo ========================================
echo 编译完成！
echo 可执行文件位置: dist\TraeAutoRegister.exe
echo 配置文件位置: dist\config.json
echo ========================================
pause
