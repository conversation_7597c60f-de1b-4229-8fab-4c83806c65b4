using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace TraeAutoRegister.Services
{
    public class TemporamEmailService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly Random _random;
        
        private const string TemporamBaseUrl = "https://www.temporam.com";
        private const string TemporamApiUrl = "https://www.temporam.com/api";
        private const string TemporamEmailUrl = "https://www.temporam.com/zh/ems";

        public TemporamEmailService()
        {
            var handler = new HttpClientHandler()
            {
                UseCookies = true
            };

            _httpClient = new HttpClient(handler);
            _random = new Random();
            SetupHttpClient();
        }

        private void SetupHttpClient()
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
            _httpClient.DefaultRequestHeaders.Add("Referer", TemporamEmailUrl);
        }

        public async Task<string> GetEducationEmailAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 尝试从Temporam获取教育邮箱...");

                // 设置很短的超时，快速失败
                using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts.CancelAfter(TimeSpan.FromSeconds(3)); // 只等3秒

                try
                {
                    // 1. 快速访问主页面
                    System.Diagnostics.Debug.WriteLine("🌐 访问Temporam主页...");
                    var mainPageResponse = await _httpClient.GetAsync(TemporamEmailUrl, cts.Token);
                    if (!mainPageResponse.IsSuccessStatusCode)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 访问主页面失败: {mainPageResponse.StatusCode}");
                        return GenerateFallbackEducationEmail();
                    }

                    System.Diagnostics.Debug.WriteLine("✅ 主页面访问成功");

                    // 2. 快速尝试获取教育邮箱
                    System.Diagnostics.Debug.WriteLine("📡 尝试API获取教育邮箱...");
                    var educationEmail = await TryGetEducationEmailFromApi(cts.Token);
                    if (!string.IsNullOrEmpty(educationEmail))
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ API获取成功: {educationEmail}");
                        return educationEmail;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ API未返回有效邮箱");
                    }
                }
                catch (OperationCanceledException)
                {
                    System.Diagnostics.Debug.WriteLine("⏰ Temporam访问超时（3秒）");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Temporam访问异常: {ex.Message}");
                }

                // 3. 快速生成备用邮箱
                System.Diagnostics.Debug.WriteLine("🔄 切换到备用邮箱生成...");
                return GenerateFallbackEducationEmail();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 获取教育邮箱总体异常: {ex.Message}");
                return GenerateFallbackEducationEmail();
            }
        }

        private async Task<string> TryGetEducationEmailFromApi(CancellationToken cancellationToken)
        {
            try
            {
                // 尝试调用可能的API端点获取教育邮箱
                var apiEndpoints = new[]
                {
                    "/api/email/generate?type=edu",
                    "/api/generate/edu",
                    "/api/email/edu"
                };

                foreach (var endpoint in apiEndpoints)
                {
                    try
                    {
                        var response = await _httpClient.GetAsync($"{TemporamBaseUrl}{endpoint}", cancellationToken);
                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            var email = ExtractEmailFromResponse(content);
                            if (!string.IsNullOrEmpty(email) && email.Contains("edu"))
                            {
                                System.Diagnostics.Debug.WriteLine($"从API获取到教育邮箱: {email}");
                                return email;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"API端点 {endpoint} 失败: {ex.Message}");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"API调用失败: {ex.Message}");
                return null;
            }
        }

        private string ExtractEmailFromResponse(string response)
        {
            try
            {
                // 尝试解析JSON响应
                if (response.StartsWith("{"))
                {
                    using var doc = JsonDocument.Parse(response);
                    if (doc.RootElement.TryGetProperty("email", out var emailElement))
                    {
                        return emailElement.GetString();
                    }
                    if (doc.RootElement.TryGetProperty("data", out var dataElement) &&
                        dataElement.TryGetProperty("email", out var dataEmailElement))
                    {
                        return dataEmailElement.GetString();
                    }
                }

                // 尝试用正则表达式提取邮箱
                var emailPattern = @"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]*\.edu\.[a-zA-Z]{2,}";
                var match = Regex.Match(response, emailPattern);
                if (match.Success)
                {
                    return match.Value;
                }

                // 尝试提取任何邮箱格式
                emailPattern = @"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}";
                match = Regex.Match(response, emailPattern);
                if (match.Success && match.Value.Contains("edu"))
                {
                    return match.Value;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析邮箱响应失败: {ex.Message}");
                return null;
            }
        }

        private string GenerateFallbackEducationEmail()
        {
            // 生成备用教育邮箱
            var prefixes = new[]
            {
                "student", "scholar", "academic", "research", "edu", "learn",
                "study", "university", "college", "campus", "faculty", "grad"
            };

            var domains = new[]
            {
                "mona.edu.kg",
                "temp.edu.kg", 
                "study.edu.kg",
                "academic.edu.kg"
            };

            var prefix = prefixes[_random.Next(prefixes.Length)];
            var numbers = _random.Next(100000, 999999);
            var domain = domains[_random.Next(domains.Length)];
            
            var email = $"{prefix}{numbers}@{domain}";
            System.Diagnostics.Debug.WriteLine($"生成备用教育邮箱: {email}");
            
            return email;
        }

        public async Task<List<EmailMessage>> GetEmailsAsync(string email, CancellationToken cancellationToken = default)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"检查邮箱 {email} 的邮件...");

                // 尝试获取邮件列表
                var emailsEndpoints = new[]
                {
                    $"/api/emails/{email}",
                    $"/api/inbox/{email}",
                    $"/api/messages/{email}"
                };

                foreach (var endpoint in emailsEndpoints)
                {
                    try
                    {
                        var response = await _httpClient.GetAsync($"{TemporamBaseUrl}{endpoint}", cancellationToken);
                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            var emails = ParseEmailsFromResponse(content);
                            if (emails.Count > 0)
                            {
                                System.Diagnostics.Debug.WriteLine($"找到 {emails.Count} 封邮件");
                                return emails;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"检查邮件端点 {endpoint} 失败: {ex.Message}");
                    }
                }

                return new List<EmailMessage>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取邮件失败: {ex.Message}");
                return new List<EmailMessage>();
            }
        }

        private List<EmailMessage> ParseEmailsFromResponse(string response)
        {
            var emails = new List<EmailMessage>();
            
            try
            {
                if (response.StartsWith("{") || response.StartsWith("["))
                {
                    using var doc = JsonDocument.Parse(response);
                    
                    // 处理数组格式
                    if (doc.RootElement.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var item in doc.RootElement.EnumerateArray())
                        {
                            var email = ParseEmailFromJson(item);
                            if (email != null) emails.Add(email);
                        }
                    }
                    // 处理对象格式
                    else if (doc.RootElement.TryGetProperty("emails", out var emailsArray) ||
                             doc.RootElement.TryGetProperty("messages", out emailsArray) ||
                             doc.RootElement.TryGetProperty("data", out emailsArray))
                    {
                        foreach (var item in emailsArray.EnumerateArray())
                        {
                            var email = ParseEmailFromJson(item);
                            if (email != null) emails.Add(email);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析邮件响应失败: {ex.Message}");
            }

            return emails;
        }

        private EmailMessage ParseEmailFromJson(JsonElement element)
        {
            try
            {
                var email = new EmailMessage();
                
                if (element.TryGetProperty("subject", out var subject))
                    email.Subject = subject.GetString();
                    
                if (element.TryGetProperty("from", out var from))
                    email.From = from.GetString();
                    
                if (element.TryGetProperty("body", out var body))
                    email.Body = body.GetString();
                    
                if (element.TryGetProperty("content", out var content))
                    email.Body = content.GetString();

                return email;
            }
            catch
            {
                return null;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    public class EmailMessage
    {
        public string Subject { get; set; }
        public string From { get; set; }
        public string Body { get; set; }
        public DateTime ReceivedAt { get; set; } = DateTime.Now;
    }
}
