using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using TraeAutoRegister.Services;
using TraeAutoRegister.Models;

namespace TraeAutoRegister
{
    public partial class MainWindow : Window
    {
        private readonly ConfigService _configService;
        private readonly HybridRegistrationService _registrationService;
        private CancellationTokenSource _cancellationTokenSource;
        private DispatcherTimer _timeTimer;
        private int _successCount = 0;
        private int _failedCount = 0;
        private int _totalCount = 0;
        private ProgressWindow _currentProgressWindow;

        public MainWindow()
        {
            InitializeComponent();
            _configService = new ConfigService();
            _registrationService = new HybridRegistrationService(_configService, useApiMode: true);

            // 订阅事件
            _registrationService.StatusUpdated += OnStatusUpdated;
            _registrationService.LogUpdated += OnLogUpdated;
            _registrationService.ProgressUpdated += OnProgressUpdated;

            InitializeTimer();
            LoadConfiguration();

            this.Closing += MainWindow_Closing;

            // 设置窗口可拖拽
            this.MouseLeftButtonDown += (sender, e) => this.DragMove();
        }

        private void InitializeTimer()
        {
            _timeTimer = new DispatcherTimer();
            _timeTimer.Interval = TimeSpan.FromSeconds(1);
            _timeTimer.Tick += (s, e) => {
                // 更新状态指示器
                txtStatusIndicator.Text = _cancellationTokenSource != null ? "正在运行中" : "免费使用中";
            };
            _timeTimer.Start();
        }

        private void LoadConfiguration()
        {
            try
            {
                var config = _configService.LoadConfig();
                // 配置已加载，使用默认设置
            }
            catch (Exception ex)
            {
                // 配置加载失败，使用默认设置
            }
        }

        private async void BtnStart_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证输入
                if (!int.TryParse(txtBatchCount.Text, out int batchCount) || batchCount <= 0)
                {
                    MessageBox.Show("请输入有效的批量数量（大于0的整数）", "输入错误",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (batchCount > 20)
                {
                    var result = MessageBox.Show($"您要注册 {batchCount} 个账号，数量较多可能需要较长时间。\n\n建议一次注册1-10个账号以获得最佳效果。\n\n是否继续？",
                                               "确认注册", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result == MessageBoxResult.No)
                        return;
                }

                // 显示状态面板
                StatusPanel.Visibility = Visibility.Visible;

                // 创建并显示进度窗口
                var progressWindow = new ProgressWindow();
                progressWindow.Owner = this;

                // 更新UI状态
                btnStart.IsEnabled = false;
                btnStart.Content = "注册中...";
                txtBatchCount.IsEnabled = false;

                // 重置计数器
                _successCount = 0;
                _failedCount = 0;
                _totalCount = batchCount;
                UpdateCounters();

                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = new CancellationTokenSource();
                progressWindow.SetCancellationTokenSource(_cancellationTokenSource);

                // 设置进度窗口的初始状态
                progressWindow.UpdateCounts(_successCount, _failedCount, _totalCount);
                progressWindow.UpdateStatus("准备开始注册...");
                progressWindow.AddLog("🚀 开始完全真实API模式批量注册");
                progressWindow.AddLog($"📊 计划注册 {batchCount} 个TRAE账号");
                progressWindow.AddLog($"🎓 邮箱来源: Temporam真实教育邮箱API");
                progressWindow.AddLog($"🌐 目标网站: https://www.trae.ai");
                progressWindow.AddLog($"⚠️ 注意: 完全真实操作，无模拟或备用方案");
                progressWindow.AddLog($"📋 详细日志: 每步操作都有完整的HTTP请求/响应日志");

                // 显示进度窗口
                progressWindow.Show();

                // 处理取消事件
                progressWindow.CancelRequested += (s, args) =>
                {
                    try
                    {
                        _cancellationTokenSource?.Cancel();
                    }
                    catch (ObjectDisposedException)
                    {
                        // 忽略已释放的异常
                    }
                };

                try
                {
                    // 添加调试信息
                    progressWindow.AddLog("🔧 初始化注册服务...");
                    progressWindow.AddLog($"📡 使用API模式: {_registrationService != null}");

                    _currentProgressWindow = progressWindow;
                    await StartBatchRegistrationWithProgress(batchCount, _cancellationTokenSource.Token, progressWindow);

                    // 注册完成
                    progressWindow.SetCompleted(true, _successCount, _failedCount);

                    // 显示完成消息
                    MessageBox.Show($"批量注册完成！\n\n✅ 成功: {_successCount} 个\n❌ 失败: {_failedCount} 个\n📁 账号信息已保存到文件",
                                   "注册完成", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (OperationCanceledException)
                {
                    progressWindow.UpdateStatus("❌ 已取消");
                    progressWindow.AddLog("⏹️ 注册已被用户取消");
                }
                catch (Exception ex)
                {
                    progressWindow.SetCompleted(false, _successCount, _failedCount);
                    progressWindow.ShowError($"注册过程发生错误: {ex.Message}");

                    MessageBox.Show($"注册过程发生错误:\n{ex.Message}", "错误",
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    // 恢复UI状态
                    btnStart.IsEnabled = true;
                    btnStart.Content = "一键获取账号";
                    txtBatchCount.IsEnabled = true;
                    UpdateCurrentStep("注册完成");
                    progressBar.Value = 100;

                    // 安全释放取消令牌
                    try
                    {
                        _cancellationTokenSource?.Dispose();
                    }
                    catch (ObjectDisposedException)
                    {
                        // 忽略已释放的异常
                    }
                    finally
                    {
                        _cancellationTokenSource = null;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动注册失败: {ex.Message}", "错误",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private async Task StartBatchRegistrationWithProgress(int count, CancellationToken cancellationToken, ProgressWindow progressWindow)
        {
            for (int i = 1; i <= count; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                progressWindow.UpdateStatus($"正在注册第 {i}/{count} 个账号...");
                progressWindow.AddLog($"🔄 开始注册第 {i} 个账号");

                var progress = (double)(i - 1) / count * 100;
                progressWindow.UpdateProgress(i - 1, count);

                try
                {
                    progressWindow.AddLog($"🔄 调用注册服务...");
                    progressWindow.AddLog($"🔍 _registrationService状态: {(_registrationService != null ? "已初始化" : "为null")}");

                    if (_registrationService == null)
                    {
                        throw new InvalidOperationException("注册服务未初始化");
                    }

                    progressWindow.AddLog($"📡 开始调用RegisterAccountAsync...");
                    var result = await _registrationService.RegisterAccountAsync(cancellationToken);
                    progressWindow.AddLog($"📡 RegisterAccountAsync调用完成，结果: {(result != null ? "有结果" : "null")}");

                    if (result != null && result.Success)
                    {
                        _successCount++;
                        progressWindow.ShowSuccess($"第 {i} 个账号注册成功: {result.Account?.Email}");
                    }
                    else
                    {
                        _failedCount++;
                        var errorMessage = result?.Message ?? "返回结果为null";
                        progressWindow.ShowError($"第 {i} 个账号注册失败: {errorMessage}");
                        progressWindow.AddLog($"🔍 失败详情: {errorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    _failedCount++;
                    progressWindow.ShowError($"第 {i} 个账号注册异常");
                    progressWindow.AddLog($"💥 异常类型: {ex.GetType().Name}");
                    progressWindow.AddLog($"💥 异常消息: {ex.Message}");

                    if (ex.InnerException != null)
                    {
                        progressWindow.AddLog($"🔗 内部异常: {ex.InnerException.GetType().Name}");
                        progressWindow.AddLog($"🔗 内部消息: {ex.InnerException.Message}");
                    }

                    if (!string.IsNullOrEmpty(ex.StackTrace))
                    {
                        progressWindow.AddLog($"📍 堆栈跟踪:");
                        var stackLines = ex.StackTrace.Split('\n').Take(5); // 只显示前5行
                        foreach (var line in stackLines)
                        {
                            if (!string.IsNullOrWhiteSpace(line))
                                progressWindow.AddLog($"    {line.Trim()}");
                        }
                    }
                }

                UpdateCounters();
                progressWindow.UpdateCounts(_successCount, _failedCount, _totalCount);
                progressWindow.UpdateProgress(i, count);

                // 延迟一段时间再注册下一个账号
                if (i < count && !cancellationToken.IsCancellationRequested)
                {
                    progressWindow.AddLog($"⏳ 等待 3 秒后继续...");
                    await Task.Delay(3000, cancellationToken);
                }
            }

            progressWindow.AddLog($"🎉 批量注册完成! 成功: {_successCount}, 失败: {_failedCount}");
        }

        private async Task StartBatchRegistration(int count, CancellationToken cancellationToken)
        {
            for (int i = 1; i <= count; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                LogMessage($"开始注册第 {i}/{count} 个账号");
                UpdateCurrentStep($"正在注册第 {i} 个账号...");
                
                var progress = (double)(i - 1) / count * 100;
                progressBar.Value = progress;

                try
                {
                    var result = await _registrationService.RegisterAccountAsync(cancellationToken);
                    
                    if (result.Success)
                    {
                        _successCount++;
                        LogMessage($"✓ 第 {i} 个账号注册成功: {result.Account.Email}");
                    }
                    else
                    {
                        _failedCount++;
                        LogMessage($"✗ 第 {i} 个账号注册失败: {result.Message}");
                    }
                }
                catch (Exception ex)
                {
                    _failedCount++;
                    LogMessage($"✗ 第 {i} 个账号注册异常: {ex.Message}");
                }

                UpdateCounters();
                
                // 账号间延迟
                if (i < count && !cancellationToken.IsCancellationRequested)
                {
                    LogMessage("等待下一个账号注册...");
                    await Task.Delay(3000, cancellationToken);
                }
            }

            LogMessage($"批量注册完成! 成功: {_successCount}, 失败: {_failedCount}");
        }

        private void UpdateCurrentStep(string step)
        {
            Dispatcher.Invoke(() => lblCurrentStep.Text = $"当前状态: {step}");
        }

        private void UpdateCounters()
        {
            Dispatcher.Invoke(() =>
            {
                lblSuccessCount.Text = $"成功: {_successCount}";
                lblFailedCount.Text = $"失败: {_failedCount}";
                lblTotalCount.Text = $"总计: {_totalCount}";
            });
        }

        private void LogMessage(string message)
        {
            Dispatcher.Invoke(() =>
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss");
                // 在新的简洁界面中，我们只在控制台输出日志
                System.Diagnostics.Debug.WriteLine($"[{timestamp}] {message}");
            });
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void BtnTutorial_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("使用教程:\n\n" +
                           "🎓 本工具使用教育邮箱API技术，无需打开浏览器！\n\n" +
                           "1. 输入要注册的账号数量（建议1-5个）\n" +
                           "2. 点击'一键获取账号'开始注册\n" +
                           "3. 程序会自动：\n" +
                           "   • 生成教育邮箱 (@mona.edu.kg)\n" +
                           "   • 调用TRAE API完成注册\n" +
                           "   • 自动验证教育邮箱\n" +
                           "   • 保存账号信息\n\n" +
                           "✨ 优势：\n" +
                           "• 使用教育邮箱，通过率更高\n" +
                           "• 速度更快（无需等待浏览器加载）\n" +
                           "• 更加稳定（不受浏览器影响）\n" +
                           "• 资源占用少（纯API调用）\n\n" +
                           "🎓 邮箱格式：<EMAIL>\n" +
                           "📝 注意：请确保网络连接正常",
                           "使用教程 - 教育邮箱API模式", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void BtnTestLogin_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 读取已注册的账号
                var accountsFile = "registered_accounts.txt";
                if (!System.IO.File.Exists(accountsFile))
                {
                    MessageBox.Show("没有找到已注册的账号文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var lines = await System.IO.File.ReadAllLinesAsync(accountsFile);
                if (lines.Length == 0)
                {
                    MessageBox.Show("账号文件为空", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 取最后一个注册的账号
                var lastLine = lines.LastOrDefault(l => !string.IsNullOrWhiteSpace(l));
                if (string.IsNullOrWhiteSpace(lastLine))
                {
                    MessageBox.Show("没有找到有效的账号信息", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 解析账号信息
                var parts = lastLine.Split(':');
                if (parts.Length < 2)
                {
                    MessageBox.Show("账号信息格式错误", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var email = parts[0];
                var password = parts[1].Split(' ')[0]; // 去掉时间戳部分

                // 显示测试信息
                var result = MessageBox.Show($"将测试账号登录:\n邮箱: {email}\n密码: {password}\n\n是否继续？",
                    "确认测试", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                    return;

                // 禁用按钮
                btnTestLogin.IsEnabled = false;
                btnTestLogin.Content = "测试中...";

                // 创建API服务实例
                using var apiService = new PureRealApiRegistrationService(_configService);

                // 测试登录
                var loginSuccess = await apiService.TestLoginAsync(email, password);

                if (loginSuccess)
                {
                    MessageBox.Show($"✅ 账号登录测试成功！\n\n邮箱: {email}\n密码: {password}\n\n账号可以正常使用。",
                        "测试成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"❌ 账号登录测试失败\n\n可能的原因：\n1. 需要邮箱验证\n2. 密码不正确\n3. 账号被锁定\n\n建议手动访问 https://www.trae.ai/login 进行验证",
                        "测试失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试登录时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复按钮
                btnTestLogin.IsEnabled = true;
                btnTestLogin.Content = "测试账号登录";
            }
        }

        private void BtnAccountManager_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var accountManagerWindow = new AccountManagerWindow(_configService);
                accountManagerWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开账号库管理失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnStatusUpdated(string status)
        {
            Dispatcher.Invoke(() =>
            {
                lblCurrentStep.Text = status;
            });
        }

        private void OnLogUpdated(string message)
        {
            Dispatcher.Invoke(() =>
            {
                // 在API模式下，我们可以在控制台输出日志，或者显示在状态中
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");

                // 如果有当前的ProgressWindow，将日志传递给它
                if (_currentProgressWindow != null)
                {
                    _currentProgressWindow.AddLog(message);
                }

                // 更新状态指示器的文本
                if (message.Contains("成功"))
                {
                    txtStatusIndicator.Text = "注册成功";
                }
                else if (message.Contains("失败"))
                {
                    txtStatusIndicator.Text = "注册失败";
                }
                else if (message.Contains("API"))
                {
                    txtStatusIndicator.Text = "API运行中";
                }
            });
        }

        private void OnProgressUpdated(int progress)
        {
            Dispatcher.Invoke(() =>
            {
                progressBar.Value = progress;
            });
        }

        private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            _cancellationTokenSource?.Cancel();
            _timeTimer?.Stop();
            _registrationService?.Dispose();
        }
    }
}
