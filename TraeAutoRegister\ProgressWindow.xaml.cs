using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Windows;

namespace TraeAutoRegister
{
    public partial class ProgressWindow : Window
    {
        private CancellationTokenSource _cancellationTokenSource;
        
        public event EventHandler CancelRequested;
        
        public ProgressWindow()
        {
            InitializeComponent();
            
            // 设置窗口可拖拽
            this.MouseLeftButtonDown += (sender, e) => this.DragMove();
        }

        public void SetCancellationTokenSource(CancellationTokenSource cancellationTokenSource)
        {
            _cancellationTokenSource = cancellationTokenSource;
        }

        public void UpdateStatus(string status)
        {
            Dispatcher.Invoke(() =>
            {
                txtCurrentStatus.Text = status;
                AddLog($"[状态] {status}");
            });
        }

        public void UpdateProgress(int current, int total)
        {
            Dispatcher.Invoke(() =>
            {
                if (total > 0)
                {
                    var percentage = (double)current / total * 100;
                    progressBar.Value = percentage;
                    txtProgress.Text = $"{percentage:F0}%";
                }
            });
        }

        public void UpdateCounts(int success, int failed, int total)
        {
            Dispatcher.Invoke(() =>
            {
                txtSuccessCount.Text = success.ToString();
                txtFailedCount.Text = failed.ToString();
                txtTotalCount.Text = total.ToString();
            });
        }

        public void AddLog(string message)
        {
            Dispatcher.Invoke(() =>
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
                var logEntry = $"[{timestamp}] {message}\n";

                txtLog.Text += logEntry;

                // 限制日志长度，避免内存占用过大
                if (txtLog.Text.Length > 50000)
                {
                    var lines = txtLog.Text.Split('\n');
                    txtLog.Text = string.Join('\n', lines.Skip(lines.Length / 2));
                }

                // 自动滚动到底部
                logScrollViewer.ScrollToEnd();
            });
        }

        public void ShowSuccess(string message)
        {
            Dispatcher.Invoke(() =>
            {
                AddLog($"✅ {message}");
            });
        }

        public void ShowError(string message)
        {
            Dispatcher.Invoke(() =>
            {
                AddLog($"❌ 错误: {message}");

                // 如果消息太长，分行显示
                if (message.Length > 80)
                {
                    var lines = SplitLongMessage(message, 80);
                    foreach (var line in lines.Skip(1))
                    {
                        AddLog($"    {line}");
                    }
                }
            });
        }

        private string[] SplitLongMessage(string message, int maxLength)
        {
            var result = new List<string>();
            for (int i = 0; i < message.Length; i += maxLength)
            {
                var length = Math.Min(maxLength, message.Length - i);
                result.Add(message.Substring(i, length));
            }
            return result.ToArray();
        }

        public void ShowInfo(string message)
        {
            Dispatcher.Invoke(() =>
            {
                AddLog($"ℹ️ {message}");
            });
        }

        public void SetCompleted(bool success, int successCount, int failedCount)
        {
            Dispatcher.Invoke(() =>
            {
                if (success)
                {
                    txtCurrentStatus.Text = "✅ 注册完成！";
                    AddLog($"🎉 批量注册完成！成功: {successCount}, 失败: {failedCount}");
                }
                else
                {
                    txtCurrentStatus.Text = "❌ 注册失败";
                    AddLog($"💥 注册过程失败，请检查网络连接或重试");
                }
                
                progressBar.Value = 100;
                txtProgress.Text = "100%";
                
                // 更改按钮文本
                btnCancel.Content = "关闭";
                btnMinimize.Visibility = Visibility.Collapsed;
            });
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            if (btnCancel.Content.ToString() == "关闭")
            {
                this.Close();
            }
            else
            {
                try
                {
                    // 取消注册
                    _cancellationTokenSource?.Cancel();
                    CancelRequested?.Invoke(this, EventArgs.Empty);

                    txtCurrentStatus.Text = "正在取消...";
                    AddLog("⏹️ 用户取消注册");
                    btnCancel.IsEnabled = false;
                }
                catch (ObjectDisposedException)
                {
                    // 如果已经释放，直接关闭
                    this.Close();
                }
            }
        }

        private void BtnMinimize_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        private void BtnCopyLog_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(txtLog.Text))
                {
                    Clipboard.SetText(txtLog.Text);
                    AddLog("📋 日志已复制到剪贴板");
                }
                else
                {
                    AddLog("📋 没有日志内容可复制");
                }
            }
            catch (Exception ex)
            {
                AddLog($"📋 复制失败: {ex.Message}");
            }
        }

        private void BtnClearLog_Click(object sender, RoutedEventArgs e)
        {
            txtLog.Text = "";
            AddLog("🗑️ 日志已清空");
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // 确保取消操作
                _cancellationTokenSource?.Cancel();
            }
            catch (ObjectDisposedException)
            {
                // 忽略已释放的异常
            }
            base.OnClosed(e);
        }
    }
}
