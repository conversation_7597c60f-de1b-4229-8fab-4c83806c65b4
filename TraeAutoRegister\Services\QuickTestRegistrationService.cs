using System;
using System.Threading;
using System.Threading.Tasks;
using TraeAutoRegister.Models;

namespace TraeAutoRegister.Services
{
    public class QuickTestRegistrationService : IDisposable
    {
        private readonly ConfigService _configService;
        private readonly AppConfig _config;
        private readonly Random _random;

        public QuickTestRegistrationService(ConfigService configService)
        {
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));
            _config = configService.LoadConfig() ?? new AppConfig();
            _random = new Random();
            
            System.Diagnostics.Debug.WriteLine("✅ QuickTestRegistrationService 初始化完成");
        }

        public async Task<RegistrationResult> RegisterAccountAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚀 开始快速测试注册流程");

                // 步骤1: 生成教育邮箱
                System.Diagnostics.Debug.WriteLine("📧 步骤1: 生成教育邮箱");
                var email = GenerateEducationEmail();
                System.Diagnostics.Debug.WriteLine($"✅ 生成教育邮箱: {email}");

                // 步骤2: 模拟访问TRAE页面
                System.Diagnostics.Debug.WriteLine("🌐 步骤2: 模拟访问TRAE注册页面");
                await Task.Delay(1000, cancellationToken); // 模拟网络延迟
                System.Diagnostics.Debug.WriteLine("✅ 成功访问TRAE页面");

                // 步骤3: 模拟提交注册
                System.Diagnostics.Debug.WriteLine("📤 步骤3: 模拟提交注册请求");
                await Task.Delay(1500, cancellationToken); // 模拟提交延迟
                
                // 随机决定成功或失败（80%成功率）
                var success = _random.Next(100) < 80;
                
                if (!success)
                {
                    var errorMessages = new[]
                    {
                        "邮箱已被注册",
                        "网络连接超时",
                        "服务器暂时不可用",
                        "验证码发送失败"
                    };
                    var errorMessage = errorMessages[_random.Next(errorMessages.Length)];
                    System.Diagnostics.Debug.WriteLine($"❌ 注册失败: {errorMessage}");
                    return new RegistrationResult { Success = false, Message = errorMessage };
                }

                System.Diagnostics.Debug.WriteLine("✅ 注册请求提交成功");

                // 步骤4: 模拟等待验证邮件
                System.Diagnostics.Debug.WriteLine("📬 步骤4: 模拟等待验证邮件");
                await Task.Delay(2000, cancellationToken); // 模拟等待邮件
                
                var verificationCode = _random.Next(100000, 999999).ToString();
                System.Diagnostics.Debug.WriteLine($"✅ 模拟收到验证码: {verificationCode}");

                // 步骤5: 模拟邮箱验证
                System.Diagnostics.Debug.WriteLine("🔐 步骤5: 模拟邮箱验证");
                await Task.Delay(800, cancellationToken); // 模拟验证延迟
                System.Diagnostics.Debug.WriteLine("✅ 邮箱验证成功");

                // 步骤6: 创建账号信息
                System.Diagnostics.Debug.WriteLine("📝 步骤6: 创建账号信息");
                var account = new AccountInfo
                {
                    Email = email,
                    Password = _config?.Settings?.DefaultPassword ?? "AAAaaa111",
                    CreatedAt = DateTime.Now,
                    Status = "测试注册成功"
                };

                System.Diagnostics.Debug.WriteLine($"🎉 注册流程完成! 邮箱: {email}, 密码: {account.Password}");

                return new RegistrationResult
                {
                    Success = true,
                    Account = account,
                    Message = "测试注册成功"
                };
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("❌ 注册被用户取消");
                return new RegistrationResult { Success = false, Message = "注册被取消" };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 注册异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"📍 异常类型: {ex.GetType().Name}");
                return new RegistrationResult
                {
                    Success = false,
                    Message = $"注册失败: {ex.Message}"
                };
            }
        }

        private string GenerateEducationEmail()
        {
            System.Diagnostics.Debug.WriteLine("🎲 开始生成随机教育邮箱...");
            
            var prefixes = new[] 
            { 
                "student", "scholar", "academic", "research", "edu", "learn",
                "study", "university", "college", "campus", "faculty", "grad"
            };
            
            var domains = new[]
            {
                "mona.edu.kg",
                "temp.edu.kg", 
                "study.edu.kg",
                "academic.edu.kg"
            };

            var prefix = prefixes[_random.Next(prefixes.Length)];
            var numbers = _random.Next(100000, 999999);
            var domain = domains[_random.Next(domains.Length)];
            
            var email = $"{prefix}{numbers}@{domain}";
            System.Diagnostics.Debug.WriteLine($"📧 生成邮箱: 前缀={prefix}, 数字={numbers}, 域名={domain}");
            System.Diagnostics.Debug.WriteLine($"📧 完整邮箱: {email}");
            
            return email;
        }

        public void Dispose()
        {
            System.Diagnostics.Debug.WriteLine("🗑️ QuickTestRegistrationService 已释放");
        }
    }
}
